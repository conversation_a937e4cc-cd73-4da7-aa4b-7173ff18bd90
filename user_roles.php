<?php
/**
 * صفحة تعيين الأدوار للمستخدمين
 * User Roles Assignment Page
 */

define('APP_INIT', true);
require_once 'includes/init.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('user_management');

// إنشاء مثيلات النماذج
$advancedRoleModel = new AdvancedRole();
$userModel = new User();

// معالجة العمليات
$action = $_GET['action'] ?? 'list';
$userId = $_GET['user_id'] ?? null;
$message = '';
$messageType = '';

// معالجة تعيين دور
if ($action === 'assign' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    if (verifyCSRFToken($csrfToken)) {
        $userId = (int)($_POST['user_id'] ?? 0);
        $roleId = (int)($_POST['role_id'] ?? 0);
        $restrictions = [];
        
        // معالجة القيود الزمنية
        if (!empty($_POST['time_start']) && !empty($_POST['time_end'])) {
            $restrictions['time_restrictions'] = [
                'start' => $_POST['time_start'],
                'end' => $_POST['time_end']
            ];
        }
        
        // معالجة قيود IP
        if (!empty($_POST['ip_restrictions'])) {
            $ipList = array_map('trim', explode(',', $_POST['ip_restrictions']));
            $restrictions['ip_restrictions'] = array_filter($ipList);
        }
        
        if ($userId && $roleId) {
            if ($advancedRoleModel->assignRoleToUser($userId, $roleId, $restrictions)) {
                $message = 'تم تعيين الدور بنجاح';
                $messageType = 'success';
            } else {
                $message = 'فشل في تعيين الدور';
                $messageType = 'error';
            }
        } else {
            $message = 'يرجى اختيار المستخدم والدور';
            $messageType = 'error';
        }
    } else {
        $message = 'طلب غير صالح';
        $messageType = 'error';
    }
}

// معالجة إلغاء تعيين دور
if ($action === 'unassign' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    if (verifyCSRFToken($csrfToken)) {
        $userId = (int)($_POST['user_id'] ?? 0);
        $roleId = (int)($_POST['role_id'] ?? 0);
        
        if ($userId && $roleId) {
            if ($advancedRoleModel->unassignRoleFromUser($userId, $roleId)) {
                $message = 'تم إلغاء تعيين الدور بنجاح';
                $messageType = 'success';
            } else {
                $message = 'فشل في إلغاء تعيين الدور';
                $messageType = 'error';
            }
        }
    } else {
        $message = 'طلب غير صالح';
        $messageType = 'error';
    }
}

// الحصول على البيانات
$users = $userModel->getAllUsers();
$roles = $advancedRoleModel->getAllRoles();
$userRoles = [];

if ($userId) {
    $userRoles = $advancedRoleModel->getUserRoles($userId);
}

$pageTitle = 'تعيين الأدوار للمستخدمين';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- خط عربي -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- أيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        
        .user-card {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: all 0.2s;
        }
        
        .user-card:hover {
            border-color: #3b82f6;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .role-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            margin: 2px;
        }
        
        .role-badge-system {
            background-color: #fef3c7;
            color: #92400e;
        }
        
        .role-badge-functional {
            background-color: #dbeafe;
            color: #1e40af;
        }
        
        .role-badge-custom {
            background-color: #f3e8ff;
            color: #7c3aed;
        }
        
        .restrictions-info {
            background-color: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 8px;
            margin-top: 8px;
            font-size: 12px;
            color: #6b7280;
        }
    </style>
</head>
<body class="bg-gray-50">

    <!-- شريط التنقل العلوي -->
    <nav class="gradient-bg shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="dashboard.php" class="flex-shrink-0 flex items-center text-white hover:text-gray-200">
                        <i class="fas fa-arrow-right text-xl ml-3"></i>
                        <span>العودة للوحة التحكم</span>
                    </a>
                </div>
                
                <div class="flex items-center">
                    <h1 class="text-white text-xl font-bold">
                        <i class="fas fa-users-cog ml-2"></i>
                        <?php echo $pageTitle; ?>
                    </h1>
                </div>
                
                <div class="flex items-center space-x-4 space-x-reverse">
                    <a href="advanced_roles.php" class="text-white hover:text-gray-200 transition-colors duration-200">
                        <i class="fas fa-user-shield ml-1"></i>
                        إدارة الأدوار
                    </a>
                    
                    <!-- مكون الإشعارات -->
                    <?php include 'includes/notification_widget.php'; ?>
                    
                    <div class="text-white text-sm">
                        <i class="fas fa-user ml-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </div>
                    <a href="logout.php" class="text-white hover:text-gray-200 transition-colors duration-200">
                        <i class="fas fa-sign-out-alt ml-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        
        <!-- عرض الرسائل -->
        <?php if (!empty($message)): ?>
            <div class="mb-6">
                <div class="<?php echo $messageType === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700'; ?> px-4 py-3 rounded">
                    <i class="fas <?php echo $messageType === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?> ml-2"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            </div>
        <?php endif; ?>
        
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            
            <!-- قائمة المستخدمين -->
            <div class="lg:col-span-2">
                <div class="bg-white shadow-lg rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">
                            <i class="fas fa-users ml-2"></i>
                            المستخدمين وأدوارهم
                        </h3>
                    </div>
                    
                    <div class="p-6">
                        <?php if (empty($users)): ?>
                            <div class="text-center py-12">
                                <i class="fas fa-users text-gray-400 text-6xl mb-4"></i>
                                <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد مستخدمين</h3>
                                <p class="text-gray-500">لم يتم إنشاء أي مستخدمين بعد</p>
                            </div>
                        <?php else: ?>
                            <div class="space-y-4">
                                <?php foreach ($users as $user): 
                                    $currentUserRoles = $advancedRoleModel->getUserRoles($user['user_id']);
                                ?>
                                    <div class="user-card">
                                        <div class="flex justify-between items-start">
                                            <div class="flex-1">
                                                <h4 class="text-lg font-semibold text-gray-900">
                                                    <?php echo htmlspecialchars($user['full_name']); ?>
                                                    <span class="text-sm text-gray-500">(<?php echo htmlspecialchars($user['username']); ?>)</span>
                                                </h4>
                                                
                                                <div class="mt-2">
                                                    <p class="text-sm text-gray-600">
                                                        <i class="fas fa-envelope ml-1"></i>
                                                        <?php echo htmlspecialchars($user['email']); ?>
                                                    </p>
                                                    <p class="text-sm text-gray-600">
                                                        <i class="fas fa-circle ml-1 <?php echo $user['is_active'] ? 'text-green-500' : 'text-red-500'; ?>"></i>
                                                        <?php echo $user['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                                    </p>
                                                </div>
                                                
                                                <!-- الأدوار الحالية -->
                                                <div class="mt-3">
                                                    <p class="text-sm font-medium text-gray-700 mb-2">الأدوار الحالية:</p>
                                                    <?php if (empty($currentUserRoles)): ?>
                                                        <span class="text-sm text-gray-500">لا توجد أدوار مُعيَّنة</span>
                                                    <?php else: ?>
                                                        <div class="flex flex-wrap">
                                                            <?php foreach ($currentUserRoles as $role): ?>
                                                                <div class="role-badge role-badge-<?php echo $role['role_type']; ?>">
                                                                    <?php echo htmlspecialchars($role['role_name']); ?>
                                                                    
                                                                    <!-- زر إلغاء التعيين -->
                                                                    <form method="POST" action="?action=unassign" class="inline mr-2">
                                                                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                                        <input type="hidden" name="user_id" value="<?php echo $user['user_id']; ?>">
                                                                        <input type="hidden" name="role_id" value="<?php echo $role['role_id']; ?>">
                                                                        <button type="submit" onclick="return confirm('هل أنت متأكد من إلغاء تعيين هذا الدور؟')" 
                                                                                class="text-red-600 hover:text-red-800 text-xs">
                                                                            <i class="fas fa-times"></i>
                                                                        </button>
                                                                    </form>
                                                                </div>
                                                                
                                                                <!-- عرض القيود إن وجدت -->
                                                                <?php if ($role['restrictions']): 
                                                                    $restrictions = json_decode($role['restrictions'], true);
                                                                ?>
                                                                    <div class="restrictions-info w-full">
                                                                        <strong>القيود:</strong>
                                                                        <?php if (isset($restrictions['time_restrictions'])): ?>
                                                                            <span>الوقت: <?php echo $restrictions['time_restrictions']['start']; ?> - <?php echo $restrictions['time_restrictions']['end']; ?></span>
                                                                        <?php endif; ?>
                                                                        <?php if (isset($restrictions['ip_restrictions'])): ?>
                                                                            <span>IP: <?php echo implode(', ', $restrictions['ip_restrictions']); ?></span>
                                                                        <?php endif; ?>
                                                                    </div>
                                                                <?php endif; ?>
                                                            <?php endforeach; ?>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            
                                            <!-- معلومات إضافية -->
                                            <div class="text-sm text-gray-500">
                                                <p>آخر دخول: <?php echo $user['last_login'] ? formatDateTime($user['last_login']) : 'لم يسجل دخول'; ?></p>
                                                <p>تاريخ الإنشاء: <?php echo formatDateTime($user['created_at']); ?></p>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- نموذج تعيين دور -->
            <div class="lg:col-span-1">
                <div class="bg-white shadow-lg rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">
                            <i class="fas fa-user-plus ml-2"></i>
                            تعيين دور جديد
                        </h3>
                    </div>

                    <div class="p-6">
                        <form method="POST" action="?action=assign">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                            <!-- اختيار المستخدم -->
                            <div class="mb-6">
                                <label for="user_id" class="block text-sm font-medium text-gray-700 mb-2">
                                    المستخدم <span class="text-red-500">*</span>
                                </label>
                                <select id="user_id" name="user_id" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">اختر المستخدم</option>
                                    <?php foreach ($users as $user): ?>
                                        <option value="<?php echo $user['user_id']; ?>">
                                            <?php echo htmlspecialchars($user['full_name']) . ' (' . htmlspecialchars($user['username']) . ')'; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- اختيار الدور -->
                            <div class="mb-6">
                                <label for="role_id" class="block text-sm font-medium text-gray-700 mb-2">
                                    الدور <span class="text-red-500">*</span>
                                </label>
                                <select id="role_id" name="role_id" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">اختر الدور</option>
                                    <?php foreach ($roles as $role): ?>
                                        <option value="<?php echo $role['role_id']; ?>">
                                            <?php echo htmlspecialchars($role['role_name']); ?>
                                            <?php if ($role['is_system_role']): ?>
                                                (نظام)
                                            <?php endif; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- القيود الزمنية -->
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    القيود الزمنية (اختياري)
                                </label>
                                <div class="grid grid-cols-2 gap-3">
                                    <div>
                                        <label for="time_start" class="block text-xs text-gray-500 mb-1">من</label>
                                        <input type="time" id="time_start" name="time_start"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    </div>
                                    <div>
                                        <label for="time_end" class="block text-xs text-gray-500 mb-1">إلى</label>
                                        <input type="time" id="time_end" name="time_end"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    </div>
                                </div>
                            </div>

                            <!-- قيود IP -->
                            <div class="mb-6">
                                <label for="ip_restrictions" class="block text-sm font-medium text-gray-700 mb-2">
                                    قيود عناوين IP (اختياري)
                                </label>
                                <input type="text" id="ip_restrictions" name="ip_restrictions"
                                       placeholder="***********, ***********"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <p class="text-xs text-gray-500 mt-1">افصل بين عناوين IP بفاصلة</p>
                            </div>

                            <!-- زر التعيين -->
                            <button type="submit" class="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-user-plus ml-2"></i>
                                تعيين الدور
                            </button>
                        </form>
                    </div>
                </div>

                <!-- معلومات الأدوار -->
                <div class="bg-white shadow-lg rounded-lg mt-6">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">
                            <i class="fas fa-info-circle ml-2"></i>
                            الأدوار المتاحة
                        </h3>
                    </div>

                    <div class="p-6">
                        <div class="space-y-3">
                            <?php foreach ($roles as $role): ?>
                                <div class="border border-gray-200 rounded-lg p-3">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <h4 class="font-medium text-gray-900"><?php echo htmlspecialchars($role['role_name']); ?></h4>
                                            <p class="text-sm text-gray-600"><?php echo htmlspecialchars($role['role_description']); ?></p>
                                        </div>
                                        <span class="role-badge role-badge-<?php echo $role['role_type']; ?>">
                                            <?php
                                            $typeLabels = ['system' => 'نظام', 'functional' => 'وظيفي', 'custom' => 'مخصص'];
                                            echo $typeLabels[$role['role_type']] ?? $role['role_type'];
                                            ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>

        </div>

    </div>

</body>
</html>

    </div>

    <!-- نافذة تعيين دور سريع -->
    <div id="assignModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">تعيين دور سريع</h3>
                </div>

                <form method="POST" action="?action=assign">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" id="modal_user_id" name="user_id" value="">

                    <div class="p-6">
                        <p class="text-sm text-gray-600 mb-4">
                            تعيين دور للمستخدم: <strong id="modal_user_name"></strong>
                        </p>

                        <div class="mb-4">
                            <label for="modal_role_id" class="block text-sm font-medium text-gray-700 mb-2">
                                اختر الدور
                            </label>
                            <select id="modal_role_id" name="role_id" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">اختر الدور</option>
                                <?php foreach ($roles as $role): ?>
                                    <option value="<?php echo $role['role_id']; ?>">
                                        <?php echo htmlspecialchars($role['role_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3 space-x-reverse">
                        <button type="button" onclick="closeAssignModal()"
                                class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg transition-colors duration-200">
                            إلغاء
                        </button>
                        <button type="submit"
                                class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            تعيين
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // عرض نافذة تعيين الدور
        function showAssignModal(userId, userName) {
            document.getElementById('modal_user_id').value = userId;
            document.getElementById('modal_user_name').textContent = userName;
            document.getElementById('assignModal').classList.remove('hidden');
        }

        // إغلاق نافذة تعيين الدور
        function closeAssignModal() {
            document.getElementById('assignModal').classList.add('hidden');
        }

        // إغلاق النافذة عند النقر خارجها
        document.getElementById('assignModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeAssignModal();
            }
        });
    </script>

</body>
</html>
