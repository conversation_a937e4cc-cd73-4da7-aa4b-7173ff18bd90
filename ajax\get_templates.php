<?php
/**
 * AJAX - الحصول على قوالب الطباعة
 * Get Print Templates AJAX
 */

define('APP_INIT', true);
require_once '../includes/init.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

// التحقق من الصلاحيات
if (!hasPermission('system_settings')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'لا توجد صلاحية']);
    exit;
}

try {
    $templateModel = new PrintTemplate();
    
    // الحصول على نوع القالب
    $templateType = $_GET['type'] ?? null;
    
    // الحصول على القوالب
    $templates = $templateModel->getAllTemplates($templateType);
    
    // فلترة القوالب النشطة فقط
    $activeTemplates = array_filter($templates, function($template) {
        return $template['is_active'] == 1;
    });
    
    // إعادة ترتيب المصفوفة
    $activeTemplates = array_values($activeTemplates);
    
    echo json_encode([
        'success' => true,
        'templates' => $activeTemplates,
        'count' => count($activeTemplates)
    ]);
    
} catch (Exception $e) {
    error_log("Get templates AJAX error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الخادم'
    ]);
}

?>
