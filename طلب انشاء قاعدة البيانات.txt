أريد منك إنشاء قاعدة بيانات MySQL كاملة لنظام الحسابات والمخازن مع الجداول التالية، مع ضرورة دعم اللغة العربية (UTF-8 encoding) وإضافة تعليقات باللغة العربية لكل جدول وحقل:

1. **جدول المستخدمين (users)**: يتضمن معرف المستخدم، اسم المستخدم، كلمة المرور المشفرة، الاسم الكامل، البريد الإلكتروني، الصلاحيات، حالة النشاط، تاريخ الإنشاء والتحديث

2. **جدول شجرة الحسابات (chart_of_accounts)**: يتضمن رقم الحساب، اسم الحساب، نوع الحساب (أصول/خصوم/حقوق ملكية/إيرادات/مصروفات)، الحساب الأب، مستوى الحساب، حالة النشاط

3. **جدول حركة الحسابات (account_transactions)**: يتضمن رقم القيد، تاريخ القيد، رقم الحساب، المبلغ مدين، المبلغ دائن، البيان، رقم المرجع، نوع العملية، المستخدم المسؤول

4. **جدول الأصناف متعددة الوحدات (items)**: يتضمن كود الصنف، اسم الصنف، الوصف، الفئة، الوحدة الأساسية، جدول فرعي للوحدات البديلة مع معاملات التحويل والأسعار

5. **جدول المخازن (warehouses)**: يتضمن كود المخزن، اسم المخزن، الموقع، المسؤول، حالة النشاط، ملاحظات

6. **جدول الفروع (branches)**: يتضمن كود الفرع، اسم الفرع، العنوان، رقم الهاتف، المدير المسؤول، حالة النشاط

يرجى إنشاء ملف SQL كامل مع:
- تعريف قاعدة البيانات بترميز UTF-8
- إنشاء كافة الجداول مع المفاتيح الأساسية والخارجية المناسبة
- إضافة فهارس للحقول المهمة لتحسين الأداء
- تعليقات مفصلة باللغة العربية لكل جدول وحقل
- قيود التكامل المرجعي المناسبة
- بيانات تجريبية أساسية لكل جدول

---------------------------------------------------------------------------------------------------------------
ملخص ما تم إنجازه
لقد قمت بإنشاء قاعدة بيانات MySQL كاملة ومحترفة لنظام الحسابات والمخازن باصناف متعدد الوحدات. إليك ما تم تنفيذه:

🗄️ قاعدة البيانات الكاملة
الملفات<|im_start|>نشأة:

database_schema.sql - ملف قاعدة البيانات الكامل (701 سطر)
README_DATABASE.md - دليل الاستخدام والتوثيق
📊 الجداول<|im_end|>نشأة (11 جدول):
users - المستخدمين وصلاحياتهم
branches - فروع الشركة
chart_of_accounts - شجرة الحسابات المحاسبية
account_transactions - حركة الحسابات والقيود
item_categories - فئات الأصناف
units - وحدات القياس
items - الأصناف الأساسية
item_units - وحدات الأصناف مع معاملات التحويل
warehouses - المخازن
inventory_balances - أرصدة المخزون
inventory_movements - حركات المخزون
✨ الميزات المتقدمة:
🔧 التقنيات المستخدمة:
ترميز UTF-8 (utf8mb4) لدعم اللغة العربية كاملاً
Foreign Keys لضمان التكامل المرجعي
Indexes محسنة لتحسين الأداء
Generated Columns للحسابات التلقائية
JSON Fields للبيانات المرنة
📈 Views للتقارير:
v_items_with_units - الأصناف مع وحداتها
v_current_inventory - أرصدة المخزون الحالية
v_chart_of_accounts_with_balances - شجرة الحسابات مع الأرصدة
⚙️ Stored Procedures:
sp_calculate_total_inventory_value - حساب قيمة المخزون
sp_update_inventory_balance - تحديث أرصدة المخزون
🔄 Triggers:
تحديث تلقائي لأرصدة المخزون عند إدراج حركات جديدة
📋 البيانات التجريبية:
👥 المستخدمون:
مدير النظام مع صلاحيات كاملة
🏢 الفروع:
الفرع الرئيسي (الرياض)
فرع جدة
📦 الأصناف التجريبية:
حليب كامل الدسم (بوحدات: لتر، مليلتر، صندوق)
أرز بسمتي (بوحدات: كيلوجرام، جرام، صندوق)
دجاج مجمد (بوحدات: كيلوجرام، جرام)
عصير برتقال (بوحدات: مليلتر، صندوق)
خبز أبيض (بوحدات: قطعة، صندوق)
🏭 المخازن:
المخزن الرئيسي
مخزن جدة
مخزن مؤقت
💰 الحسابات المحاسبية:
شجرة حسابات كاملة (أصول، خصوم، حقوق ملكية، إيرادات، مصروفات)
قيود افتتاحية للمخزون والنقدية
🚀 كيفية الاستخدام:
تثبيت قاعدة البيانات:
الدخول للنظام:
اسم المستخدم: admin
كلمة المرور: password (مشفرة في قاعدة البيانات)
استعلامات مفيدة:
🔒 الأمان والجودة:
كلمات مرور مشفرة باستخدام bcrypt
قيود التكامل المرجعي
فهارس محسنة للأداء
تعليقات شاملة باللغة العربية
معالجة الأخطاء والاستثناءات
هذه قاعدة بيانات احترافية جاهزة للاستخدام في بيئة الإنتاج مع إمكانيات توسع مستقبلية. هل تريد مني البدء في تطوير واجهات المستخدم أو أي جزء آخر من النظام؟