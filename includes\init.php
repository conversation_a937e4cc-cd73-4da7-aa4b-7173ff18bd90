<?php
/**
 * ملف التهيئة الرئيسي
 * Main Initialization File
 */

// منع الوصول المباشر
if (!defined('APP_INIT')) {
    die('Access denied');
}

// تضمين ملفات الإعدادات
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/Database.php';

// بدء الجلسة الآمنة
startSecureSession();

// تضمين النماذج
require_once 'models/User.php';
require_once 'models/Role.php';
require_once 'models/ActivityLog.php';
require_once 'models/Backup.php';
require_once 'models/Notification.php';
require_once 'models/Setting.php';
require_once 'models/Item.php';
require_once 'models/Category.php';
require_once 'models/Unit.php';
require_once 'models/Warehouse.php';
require_once 'models/Inventory.php';
require_once 'models/Branch.php';
require_once 'models/Account.php';
require_once 'models/Transaction.php';
require_once 'models/Customer.php';
require_once 'models/Invoice.php';
require_once 'models/Payment.php';
require_once 'models/Report.php';
require_once 'models/PrintTemplate.php';
require_once 'models/Analytics.php';
require_once 'models/Attachment.php';
require_once 'models/AdvancedRole.php';

// إنشاء مثيل قاعدة البيانات
try {
    $database = Database::getInstance();
} catch (Exception $e) {
    if (DEBUG_MODE) {
        die("Database connection failed: " . $e->getMessage());
    } else {
        die("حدث خطأ في النظام. يرجى المحاولة لاحقاً.");
    }
}

// إنشاء مجلدات مطلوبة إذا لم تكن موجودة
$requiredDirs = ['logs', 'uploads', 'reports', 'temp'];
foreach ($requiredDirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// دالة للحصول على URL الأساسي
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $path = dirname($_SERVER['SCRIPT_NAME']);
    return $protocol . '://' . $host . $path;
}

// دالة لتضمين العرض
function includeView($viewName, $data = []) {
    extract($data);
    $viewFile = "views/{$viewName}.php";
    
    if (file_exists($viewFile)) {
        include $viewFile;
    } else {
        die("View file not found: {$viewName}");
    }
}

// دالة لتضمين التخطيط
function includeLayout($layoutName, $content, $data = []) {
    $data['content'] = $content;
    extract($data);
    $layoutFile = "views/layouts/{$layoutName}.php";
    
    if (file_exists($layoutFile)) {
        include $layoutFile;
    } else {
        die("Layout file not found: {$layoutName}");
    }
}

// معالج الأخطاء المخصص
function customErrorHandler($errno, $errstr, $errfile, $errline) {
    if (!(error_reporting() & $errno)) {
        return false;
    }
    
    $errorMessage = "Error [{$errno}]: {$errstr} in {$errfile} on line {$errline}";
    
    if (DEBUG_MODE) {
        echo "<div style='background: #ffebee; border: 1px solid #f44336; padding: 10px; margin: 10px; border-radius: 4px;'>";
        echo "<strong>خطأ في النظام:</strong><br>";
        echo htmlspecialchars($errorMessage);
        echo "</div>";
    }
    
    error_log($errorMessage);
    return true;
}

// معالج الاستثناءات المخصص
function customExceptionHandler($exception) {
    $errorMessage = "Uncaught exception: " . $exception->getMessage() . 
                   " in " . $exception->getFile() . 
                   " on line " . $exception->getLine();
    
    if (DEBUG_MODE) {
        echo "<div style='background: #ffebee; border: 1px solid #f44336; padding: 10px; margin: 10px; border-radius: 4px;'>";
        echo "<strong>استثناء غير معالج:</strong><br>";
        echo htmlspecialchars($exception->getMessage());
        echo "<br><strong>الملف:</strong> " . htmlspecialchars($exception->getFile());
        echo "<br><strong>السطر:</strong> " . $exception->getLine();
        echo "</div>";
    } else {
        echo "<div style='background: #ffebee; border: 1px solid #f44336; padding: 10px; margin: 10px; border-radius: 4px;'>";
        echo "حدث خطأ في النظام. يرجى المحاولة لاحقاً.";
        echo "</div>";
    }
    
    error_log($errorMessage);
}

// تعيين معالجات الأخطاء
set_error_handler('customErrorHandler');
set_exception_handler('customExceptionHandler');

// دالة للتحقق من الصلاحيات وإعادة التوجيه
function requireLogin($redirectTo = 'login.php') {
    if (!isLoggedIn()) {
        redirect($redirectTo);
    }
}

// دالة للتحقق من الصلاحيات المحددة
function requirePermission($permission, $redirectTo = 'dashboard.php') {
    requireLogin();
    
    if (!hasPermission($permission)) {
        setAlert('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error');
        redirect($redirectTo);
    }
}

// دالة لتنظيف المخرجات
function cleanOutput() {
    if (ob_get_level()) {
        ob_end_clean();
    }
    ob_start();
}

// دالة لإنهاء المخرجات
function flushOutput() {
    if (ob_get_level()) {
        ob_end_flush();
    }
}

// إعدادات إضافية للأمان
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// إعداد المنطقة الزمنية للجلسة
if (isLoggedIn() && isset($_SESSION['timezone'])) {
    date_default_timezone_set($_SESSION['timezone']);
}

?>
