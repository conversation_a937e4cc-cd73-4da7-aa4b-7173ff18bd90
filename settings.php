<?php
/**
 * صفحة إعدادات النظام
 * System Settings Page
 */

define('APP_INIT', true);
require_once 'includes/init.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('system_settings');

// إنشاء مثيل نموذج الإعدادات
$settingModel = new Setting();

// معالجة العمليات
$action = $_GET['action'] ?? 'company';
$message = '';
$messageType = '';

// معالجة حفظ الإعدادات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    if (verifyCSRFToken($csrfToken)) {
        $success = false;
        
        switch ($action) {
            case 'company':
                $companySettings = [
                    'name' => sanitizeInput($_POST['company_name'] ?? ''),
                    'address' => sanitizeInput($_POST['company_address'] ?? ''),
                    'phone' => sanitizeInput($_POST['company_phone'] ?? ''),
                    'email' => sanitizeInput($_POST['company_email'] ?? ''),
                    'website' => sanitizeInput($_POST['company_website'] ?? ''),
                    'tax_number' => sanitizeInput($_POST['company_tax_number'] ?? ''),
                    'commercial_register' => sanitizeInput($_POST['company_commercial_register'] ?? ''),
                ];
                $success = $settingModel->updateCompanySettings($companySettings);
                break;
                
            case 'system':
                $systemSettings = [
                    'timezone' => sanitizeInput($_POST['system_timezone'] ?? 'Asia/Riyadh'),
                    'date_format' => sanitizeInput($_POST['system_date_format'] ?? 'Y-m-d'),
                    'time_format' => sanitizeInput($_POST['system_time_format'] ?? 'H:i:s'),
                    'language' => sanitizeInput($_POST['system_language'] ?? 'ar'),
                    'items_per_page' => (int)($_POST['system_items_per_page'] ?? 20),
                    'session_timeout' => (int)($_POST['system_session_timeout'] ?? 3600),
                    'backup_auto' => isset($_POST['system_backup_auto']) ? '1' : '0',
                    'maintenance_mode' => isset($_POST['system_maintenance_mode']) ? '1' : '0',
                ];
                $success = $settingModel->updateSystemSettings($systemSettings);
                break;
                
            case 'currency':
                $currencySettings = [
                    'code' => sanitizeInput($_POST['currency_code'] ?? 'SAR'),
                    'symbol' => sanitizeInput($_POST['currency_symbol'] ?? 'ريال'),
                    'position' => sanitizeInput($_POST['currency_position'] ?? 'after'),
                    'decimal_places' => (int)($_POST['currency_decimal_places'] ?? 2),
                    'thousands_separator' => sanitizeInput($_POST['currency_thousands_separator'] ?? ','),
                    'decimal_separator' => sanitizeInput($_POST['currency_decimal_separator'] ?? '.'),
                ];
                $success = $settingModel->updateCurrencySettings($currencySettings);
                break;
                
            case 'tax':
                $taxSettings = [
                    'enabled' => isset($_POST['tax_enabled']) ? '1' : '0',
                    'default_rate' => (float)($_POST['tax_default_rate'] ?? 15),
                    'inclusive' => isset($_POST['tax_inclusive']) ? '1' : '0',
                    'number_required' => isset($_POST['tax_number_required']) ? '1' : '0',
                ];
                $success = $settingModel->updateTaxSettings($taxSettings);
                break;
                
            case 'invoice':
                $invoiceSettings = [
                    'prefix_sales' => sanitizeInput($_POST['invoice_prefix_sales'] ?? 'INV'),
                    'prefix_purchase' => sanitizeInput($_POST['invoice_prefix_purchase'] ?? 'PUR'),
                    'auto_number' => isset($_POST['invoice_auto_number']) ? '1' : '0',
                    'terms' => sanitizeInput($_POST['invoice_terms'] ?? ''),
                    'footer' => sanitizeInput($_POST['invoice_footer'] ?? ''),
                    'due_days' => (int)($_POST['invoice_due_days'] ?? 30),
                    'late_fee' => (float)($_POST['invoice_late_fee'] ?? 0),
                ];
                $success = $settingModel->updateInvoiceSettings($invoiceSettings);
                break;
        }
        
        if ($success) {
            $message = 'تم حفظ الإعدادات بنجاح';
            $messageType = 'success';
        } else {
            $message = 'فشل في حفظ الإعدادات';
            $messageType = 'error';
        }
    } else {
        $message = 'طلب غير صالح';
        $messageType = 'error';
    }
}

// الحصول على الإعدادات الحالية
$companySettings = $settingModel->getCompanySettings();
$systemSettings = $settingModel->getSystemSettings();
$currencySettings = $settingModel->getCurrencySettings();
$taxSettings = $settingModel->getTaxSettings();
$invoiceSettings = $settingModel->getInvoiceSettings();

$pageTitle = 'إعدادات النظام';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- خط عربي -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- أيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .tab-active { background-color: #3b82f6; color: white; }
        .tab-inactive { background-color: #f3f4f6; color: #6b7280; }
        .tab-inactive:hover { background-color: #e5e7eb; }
    </style>
</head>
<body class="bg-gray-50">

    <!-- شريط التنقل العلوي -->
    <nav class="gradient-bg shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="dashboard.php" class="flex-shrink-0 flex items-center text-white hover:text-gray-200">
                        <i class="fas fa-arrow-right text-xl ml-3"></i>
                        <span>العودة للوحة التحكم</span>
                    </a>
                </div>
                
                <div class="flex items-center">
                    <h1 class="text-white text-xl font-bold">
                        <i class="fas fa-cog ml-2"></i>
                        <?php echo $pageTitle; ?>
                    </h1>
                </div>
                
                <div class="flex items-center space-x-4 space-x-reverse">
                    <!-- مكون الإشعارات -->
                    <?php include 'includes/notification_widget.php'; ?>
                    
                    <div class="text-white text-sm">
                        <i class="fas fa-user ml-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </div>
                    <a href="logout.php" class="text-white hover:text-gray-200 transition-colors duration-200">
                        <i class="fas fa-sign-out-alt ml-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        
        <!-- عرض الرسائل -->
        <?php if (!empty($message)): ?>
            <div class="mb-6">
                <div class="<?php echo $messageType === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700'; ?> px-4 py-3 rounded">
                    <i class="fas <?php echo $messageType === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?> ml-2"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- تبويبات الإعدادات -->
        <div class="bg-white shadow-lg rounded-lg">
            
            <!-- رؤوس التبويبات -->
            <div class="border-b border-gray-200">
                <nav class="flex space-x-8 space-x-reverse px-6">
                    <a href="?action=company" 
                       class="py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 <?php echo $action === 'company' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?>">
                        <i class="fas fa-building ml-2"></i>
                        معلومات الشركة
                    </a>
                    <a href="?action=system" 
                       class="py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 <?php echo $action === 'system' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?>">
                        <i class="fas fa-cogs ml-2"></i>
                        إعدادات النظام
                    </a>
                    <a href="?action=currency" 
                       class="py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 <?php echo $action === 'currency' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?>">
                        <i class="fas fa-coins ml-2"></i>
                        إعدادات العملة
                    </a>
                    <a href="?action=tax" 
                       class="py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 <?php echo $action === 'tax' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?>">
                        <i class="fas fa-percentage ml-2"></i>
                        إعدادات الضرائب
                    </a>
                    <a href="?action=invoice"
                       class="py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 <?php echo $action === 'invoice' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?>">
                        <i class="fas fa-file-invoice ml-2"></i>
                        إعدادات الفواتير
                    </a>
                    <a href="print_templates.php"
                       class="py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300">
                        <i class="fas fa-print ml-2"></i>
                        قوالب الطباعة
                    </a>
                </nav>
            </div>
            
            <!-- محتوى التبويبات -->
            <div class="p-6">
                
                <?php if ($action === 'company'): ?>
                    <!-- إعدادات الشركة -->
                    <form method="POST" action="?action=company">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            
                            <!-- اسم الشركة -->
                            <div>
                                <label for="company_name" class="block text-sm font-medium text-gray-700 mb-2">
                                    اسم الشركة <span class="text-red-500">*</span>
                                </label>
                                <input type="text" id="company_name" name="company_name" required
                                       value="<?php echo htmlspecialchars($companySettings['name'] ?? ''); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            
                            <!-- عنوان الشركة -->
                            <div>
                                <label for="company_address" class="block text-sm font-medium text-gray-700 mb-2">
                                    عنوان الشركة
                                </label>
                                <textarea id="company_address" name="company_address" rows="3"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"><?php echo htmlspecialchars($companySettings['address'] ?? ''); ?></textarea>
                            </div>
                            
                            <!-- هاتف الشركة -->
                            <div>
                                <label for="company_phone" class="block text-sm font-medium text-gray-700 mb-2">
                                    هاتف الشركة
                                </label>
                                <input type="tel" id="company_phone" name="company_phone"
                                       value="<?php echo htmlspecialchars($companySettings['phone'] ?? ''); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            
                            <!-- بريد الشركة الإلكتروني -->
                            <div>
                                <label for="company_email" class="block text-sm font-medium text-gray-700 mb-2">
                                    البريد الإلكتروني
                                </label>
                                <input type="email" id="company_email" name="company_email"
                                       value="<?php echo htmlspecialchars($companySettings['email'] ?? ''); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            
                            <!-- موقع الشركة -->
                            <div>
                                <label for="company_website" class="block text-sm font-medium text-gray-700 mb-2">
                                    موقع الشركة
                                </label>
                                <input type="url" id="company_website" name="company_website"
                                       value="<?php echo htmlspecialchars($companySettings['website'] ?? ''); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            
                            <!-- الرقم الضريبي -->
                            <div>
                                <label for="company_tax_number" class="block text-sm font-medium text-gray-700 mb-2">
                                    الرقم الضريبي
                                </label>
                                <input type="text" id="company_tax_number" name="company_tax_number"
                                       value="<?php echo htmlspecialchars($companySettings['tax_number'] ?? ''); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            
                            <!-- السجل التجاري -->
                            <div>
                                <label for="company_commercial_register" class="block text-sm font-medium text-gray-700 mb-2">
                                    السجل التجاري
                                </label>
                                <input type="text" id="company_commercial_register" name="company_commercial_register"
                                       value="<?php echo htmlspecialchars($companySettings['commercial_register'] ?? ''); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            
                        </div>
                        
                        <!-- زر الحفظ -->
                        <div class="mt-6 flex justify-end">
                            <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-save ml-2"></i>
                                حفظ الإعدادات
                            </button>
                        </div>
                        
                    </form>

                <?php elseif ($action === 'system'): ?>
                    <!-- إعدادات النظام -->
                    <form method="POST" action="?action=system">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

                            <!-- المنطقة الزمنية -->
                            <div>
                                <label for="system_timezone" class="block text-sm font-medium text-gray-700 mb-2">
                                    المنطقة الزمنية
                                </label>
                                <select id="system_timezone" name="system_timezone"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="Asia/Riyadh" <?php echo ($systemSettings['timezone'] ?? '') === 'Asia/Riyadh' ? 'selected' : ''; ?>>الرياض (Asia/Riyadh)</option>
                                    <option value="Asia/Dubai" <?php echo ($systemSettings['timezone'] ?? '') === 'Asia/Dubai' ? 'selected' : ''; ?>>دبي (Asia/Dubai)</option>
                                    <option value="Asia/Kuwait" <?php echo ($systemSettings['timezone'] ?? '') === 'Asia/Kuwait' ? 'selected' : ''; ?>>الكويت (Asia/Kuwait)</option>
                                    <option value="Asia/Qatar" <?php echo ($systemSettings['timezone'] ?? '') === 'Asia/Qatar' ? 'selected' : ''; ?>>قطر (Asia/Qatar)</option>
                                    <option value="Asia/Bahrain" <?php echo ($systemSettings['timezone'] ?? '') === 'Asia/Bahrain' ? 'selected' : ''; ?>>البحرين (Asia/Bahrain)</option>
                                </select>
                            </div>

                            <!-- تنسيق التاريخ -->
                            <div>
                                <label for="system_date_format" class="block text-sm font-medium text-gray-700 mb-2">
                                    تنسيق التاريخ
                                </label>
                                <select id="system_date_format" name="system_date_format"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="Y-m-d" <?php echo ($systemSettings['date_format'] ?? '') === 'Y-m-d' ? 'selected' : ''; ?>>2024-01-15</option>
                                    <option value="d/m/Y" <?php echo ($systemSettings['date_format'] ?? '') === 'd/m/Y' ? 'selected' : ''; ?>>15/01/2024</option>
                                    <option value="d-m-Y" <?php echo ($systemSettings['date_format'] ?? '') === 'd-m-Y' ? 'selected' : ''; ?>>15-01-2024</option>
                                    <option value="m/d/Y" <?php echo ($systemSettings['date_format'] ?? '') === 'm/d/Y' ? 'selected' : ''; ?>>01/15/2024</option>
                                </select>
                            </div>

                            <!-- تنسيق الوقت -->
                            <div>
                                <label for="system_time_format" class="block text-sm font-medium text-gray-700 mb-2">
                                    تنسيق الوقت
                                </label>
                                <select id="system_time_format" name="system_time_format"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="H:i:s" <?php echo ($systemSettings['time_format'] ?? '') === 'H:i:s' ? 'selected' : ''; ?>>24 ساعة (14:30:00)</option>
                                    <option value="h:i:s A" <?php echo ($systemSettings['time_format'] ?? '') === 'h:i:s A' ? 'selected' : ''; ?>>12 ساعة (02:30:00 PM)</option>
                                    <option value="H:i" <?php echo ($systemSettings['time_format'] ?? '') === 'H:i' ? 'selected' : ''; ?>>24 ساعة (14:30)</option>
                                    <option value="h:i A" <?php echo ($systemSettings['time_format'] ?? '') === 'h:i A' ? 'selected' : ''; ?>>12 ساعة (02:30 PM)</option>
                                </select>
                            </div>

                            <!-- لغة النظام -->
                            <div>
                                <label for="system_language" class="block text-sm font-medium text-gray-700 mb-2">
                                    لغة النظام
                                </label>
                                <select id="system_language" name="system_language"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="ar" <?php echo ($systemSettings['language'] ?? '') === 'ar' ? 'selected' : ''; ?>>العربية</option>
                                    <option value="en" <?php echo ($systemSettings['language'] ?? '') === 'en' ? 'selected' : ''; ?>>English</option>
                                </select>
                            </div>

                            <!-- عدد العناصر في الصفحة -->
                            <div>
                                <label for="system_items_per_page" class="block text-sm font-medium text-gray-700 mb-2">
                                    عدد العناصر في الصفحة
                                </label>
                                <select id="system_items_per_page" name="system_items_per_page"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="10" <?php echo ($systemSettings['items_per_page'] ?? '') == '10' ? 'selected' : ''; ?>>10</option>
                                    <option value="20" <?php echo ($systemSettings['items_per_page'] ?? '') == '20' ? 'selected' : ''; ?>>20</option>
                                    <option value="50" <?php echo ($systemSettings['items_per_page'] ?? '') == '50' ? 'selected' : ''; ?>>50</option>
                                    <option value="100" <?php echo ($systemSettings['items_per_page'] ?? '') == '100' ? 'selected' : ''; ?>>100</option>
                                </select>
                            </div>

                            <!-- مهلة انتهاء الجلسة -->
                            <div>
                                <label for="system_session_timeout" class="block text-sm font-medium text-gray-700 mb-2">
                                    مهلة انتهاء الجلسة (بالثواني)
                                </label>
                                <input type="number" id="system_session_timeout" name="system_session_timeout" min="300" max="86400"
                                       value="<?php echo htmlspecialchars($systemSettings['session_timeout'] ?? '3600'); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <p class="text-xs text-gray-500 mt-1">القيمة الافتراضية: 3600 ثانية (ساعة واحدة)</p>
                            </div>

                        </div>

                        <!-- خيارات إضافية -->
                        <div class="mt-6 space-y-4">
                            <div class="flex items-center">
                                <input type="checkbox" id="system_backup_auto" name="system_backup_auto" value="1"
                                       <?php echo ($systemSettings['backup_auto'] ?? '0') === '1' ? 'checked' : ''; ?>
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="system_backup_auto" class="mr-2 block text-sm text-gray-900">
                                    تفعيل النسخ الاحتياطي التلقائي
                                </label>
                            </div>

                            <div class="flex items-center">
                                <input type="checkbox" id="system_maintenance_mode" name="system_maintenance_mode" value="1"
                                       <?php echo ($systemSettings['maintenance_mode'] ?? '0') === '1' ? 'checked' : ''; ?>
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="system_maintenance_mode" class="mr-2 block text-sm text-gray-900">
                                    وضع الصيانة (سيمنع الوصول للنظام مؤقتاً)
                                </label>
                            </div>
                        </div>

                        <!-- زر الحفظ -->
                        <div class="mt-6 flex justify-end">
                            <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-save ml-2"></i>
                                حفظ الإعدادات
                            </button>
                        </div>

                    </form>

                <?php elseif ($action === 'currency'): ?>
                    <!-- إعدادات العملة -->
                    <form method="POST" action="?action=currency">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

                            <!-- رمز العملة -->
                            <div>
                                <label for="currency_code" class="block text-sm font-medium text-gray-700 mb-2">
                                    رمز العملة
                                </label>
                                <select id="currency_code" name="currency_code"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="SAR" <?php echo ($currencySettings['code'] ?? '') === 'SAR' ? 'selected' : ''; ?>>ريال سعودي (SAR)</option>
                                    <option value="AED" <?php echo ($currencySettings['code'] ?? '') === 'AED' ? 'selected' : ''; ?>>درهم إماراتي (AED)</option>
                                    <option value="KWD" <?php echo ($currencySettings['code'] ?? '') === 'KWD' ? 'selected' : ''; ?>>دينار كويتي (KWD)</option>
                                    <option value="QAR" <?php echo ($currencySettings['code'] ?? '') === 'QAR' ? 'selected' : ''; ?>>ريال قطري (QAR)</option>
                                    <option value="BHD" <?php echo ($currencySettings['code'] ?? '') === 'BHD' ? 'selected' : ''; ?>>دينار بحريني (BHD)</option>
                                    <option value="USD" <?php echo ($currencySettings['code'] ?? '') === 'USD' ? 'selected' : ''; ?>>دولار أمريكي (USD)</option>
                                    <option value="EUR" <?php echo ($currencySettings['code'] ?? '') === 'EUR' ? 'selected' : ''; ?>>يورو (EUR)</option>
                                </select>
                            </div>

                            <!-- رمز العملة للعرض -->
                            <div>
                                <label for="currency_symbol" class="block text-sm font-medium text-gray-700 mb-2">
                                    رمز العملة للعرض
                                </label>
                                <input type="text" id="currency_symbol" name="currency_symbol"
                                       value="<?php echo htmlspecialchars($currencySettings['symbol'] ?? 'ريال'); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>

                            <!-- موضع رمز العملة -->
                            <div>
                                <label for="currency_position" class="block text-sm font-medium text-gray-700 mb-2">
                                    موضع رمز العملة
                                </label>
                                <select id="currency_position" name="currency_position"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="before" <?php echo ($currencySettings['position'] ?? '') === 'before' ? 'selected' : ''; ?>>قبل المبلغ (ريال 100)</option>
                                    <option value="after" <?php echo ($currencySettings['position'] ?? '') === 'after' ? 'selected' : ''; ?>>بعد المبلغ (100 ريال)</option>
                                </select>
                            </div>

                            <!-- عدد الخانات العشرية -->
                            <div>
                                <label for="currency_decimal_places" class="block text-sm font-medium text-gray-700 mb-2">
                                    عدد الخانات العشرية
                                </label>
                                <select id="currency_decimal_places" name="currency_decimal_places"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="0" <?php echo ($currencySettings['decimal_places'] ?? '') == '0' ? 'selected' : ''; ?>>0 (100)</option>
                                    <option value="1" <?php echo ($currencySettings['decimal_places'] ?? '') == '1' ? 'selected' : ''; ?>>1 (100.0)</option>
                                    <option value="2" <?php echo ($currencySettings['decimal_places'] ?? '') == '2' ? 'selected' : ''; ?>>2 (100.00)</option>
                                    <option value="3" <?php echo ($currencySettings['decimal_places'] ?? '') == '3' ? 'selected' : ''; ?>>3 (100.000)</option>
                                </select>
                            </div>

                            <!-- فاصل الآلاف -->
                            <div>
                                <label for="currency_thousands_separator" class="block text-sm font-medium text-gray-700 mb-2">
                                    فاصل الآلاف
                                </label>
                                <select id="currency_thousands_separator" name="currency_thousands_separator"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="," <?php echo ($currencySettings['thousands_separator'] ?? '') === ',' ? 'selected' : ''; ?>>فاصلة (1,000)</option>
                                    <option value="." <?php echo ($currencySettings['thousands_separator'] ?? '') === '.' ? 'selected' : ''; ?>>نقطة (1.000)</option>
                                    <option value=" " <?php echo ($currencySettings['thousands_separator'] ?? '') === ' ' ? 'selected' : ''; ?>>مسافة (1 000)</option>
                                    <option value="" <?php echo ($currencySettings['thousands_separator'] ?? '') === '' ? 'selected' : ''; ?>>بدون فاصل (1000)</option>
                                </select>
                            </div>

                            <!-- فاصل العشرية -->
                            <div>
                                <label for="currency_decimal_separator" class="block text-sm font-medium text-gray-700 mb-2">
                                    فاصل العشرية
                                </label>
                                <select id="currency_decimal_separator" name="currency_decimal_separator"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="." <?php echo ($currencySettings['decimal_separator'] ?? '') === '.' ? 'selected' : ''; ?>>نقطة (100.50)</option>
                                    <option value="," <?php echo ($currencySettings['decimal_separator'] ?? '') === ',' ? 'selected' : ''; ?>>فاصلة (100,50)</option>
                                </select>
                            </div>

                        </div>

                        <!-- معاينة التنسيق -->
                        <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                            <h4 class="text-sm font-medium text-gray-700 mb-2">معاينة التنسيق:</h4>
                            <div id="currency-preview" class="text-lg font-medium text-blue-600">
                                <!-- سيتم تحديثها بـ JavaScript -->
                            </div>
                        </div>

                        <!-- زر الحفظ -->
                        <div class="mt-6 flex justify-end">
                            <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-save ml-2"></i>
                                حفظ الإعدادات
                            </button>
                        </div>

                    </form>

                <?php elseif ($action === 'tax'): ?>
                    <!-- إعدادات الضرائب -->
                    <form method="POST" action="?action=tax">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

                            <!-- معدل الضريبة الافتراضي -->
                            <div>
                                <label for="tax_default_rate" class="block text-sm font-medium text-gray-700 mb-2">
                                    معدل الضريبة الافتراضي (%)
                                </label>
                                <input type="number" id="tax_default_rate" name="tax_default_rate"
                                       min="0" max="100" step="0.01"
                                       value="<?php echo htmlspecialchars($taxSettings['default_rate'] ?? '15'); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <p class="text-xs text-gray-500 mt-1">القيمة الافتراضية: 15% (ضريبة القيمة المضافة في السعودية)</p>
                            </div>

                        </div>

                        <!-- خيارات الضرائب -->
                        <div class="mt-6 space-y-4">
                            <div class="flex items-center">
                                <input type="checkbox" id="tax_enabled" name="tax_enabled" value="1"
                                       <?php echo ($taxSettings['enabled'] ?? '1') === '1' ? 'checked' : ''; ?>
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="tax_enabled" class="mr-2 block text-sm text-gray-900">
                                    تفعيل نظام الضرائب
                                </label>
                            </div>

                            <div class="flex items-center">
                                <input type="checkbox" id="tax_inclusive" name="tax_inclusive" value="1"
                                       <?php echo ($taxSettings['inclusive'] ?? '0') === '1' ? 'checked' : ''; ?>
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="tax_inclusive" class="mr-2 block text-sm text-gray-900">
                                    الضريبة شاملة في الأسعار (بدلاً من إضافتها)
                                </label>
                            </div>

                            <div class="flex items-center">
                                <input type="checkbox" id="tax_number_required" name="tax_number_required" value="1"
                                       <?php echo ($taxSettings['number_required'] ?? '1') === '1' ? 'checked' : ''; ?>
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="tax_number_required" class="mr-2 block text-sm text-gray-900">
                                    الرقم الضريبي مطلوب للعملاء
                                </label>
                            </div>
                        </div>

                        <!-- زر الحفظ -->
                        <div class="mt-6 flex justify-end">
                            <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-save ml-2"></i>
                                حفظ الإعدادات
                            </button>
                        </div>

                    </form>

                <?php elseif ($action === 'invoice'): ?>
                    <!-- إعدادات الفواتير -->
                    <form method="POST" action="?action=invoice">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

                            <!-- بادئة فواتير المبيعات -->
                            <div>
                                <label for="invoice_prefix_sales" class="block text-sm font-medium text-gray-700 mb-2">
                                    بادئة فواتير المبيعات
                                </label>
                                <input type="text" id="invoice_prefix_sales" name="invoice_prefix_sales"
                                       value="<?php echo htmlspecialchars($invoiceSettings['prefix_sales'] ?? 'INV'); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <p class="text-xs text-gray-500 mt-1">مثال: INV-2024-0001</p>
                            </div>

                            <!-- بادئة فواتير المشتريات -->
                            <div>
                                <label for="invoice_prefix_purchase" class="block text-sm font-medium text-gray-700 mb-2">
                                    بادئة فواتير المشتريات
                                </label>
                                <input type="text" id="invoice_prefix_purchase" name="invoice_prefix_purchase"
                                       value="<?php echo htmlspecialchars($invoiceSettings['prefix_purchase'] ?? 'PUR'); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <p class="text-xs text-gray-500 mt-1">مثال: PUR-2024-0001</p>
                            </div>

                            <!-- مدة الاستحقاق الافتراضية -->
                            <div>
                                <label for="invoice_due_days" class="block text-sm font-medium text-gray-700 mb-2">
                                    مدة الاستحقاق الافتراضية (بالأيام)
                                </label>
                                <input type="number" id="invoice_due_days" name="invoice_due_days"
                                       min="0" max="365"
                                       value="<?php echo htmlspecialchars($invoiceSettings['due_days'] ?? '30'); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>

                            <!-- رسوم التأخير -->
                            <div>
                                <label for="invoice_late_fee" class="block text-sm font-medium text-gray-700 mb-2">
                                    رسوم التأخير (%)
                                </label>
                                <input type="number" id="invoice_late_fee" name="invoice_late_fee"
                                       min="0" max="100" step="0.01"
                                       value="<?php echo htmlspecialchars($invoiceSettings['late_fee'] ?? '0'); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>

                        </div>

                        <!-- شروط وأحكام الفاتورة -->
                        <div class="mt-6">
                            <label for="invoice_terms" class="block text-sm font-medium text-gray-700 mb-2">
                                شروط وأحكام الفاتورة
                            </label>
                            <textarea id="invoice_terms" name="invoice_terms" rows="4"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"><?php echo htmlspecialchars($invoiceSettings['terms'] ?? ''); ?></textarea>
                        </div>

                        <!-- تذييل الفاتورة -->
                        <div class="mt-6">
                            <label for="invoice_footer" class="block text-sm font-medium text-gray-700 mb-2">
                                تذييل الفاتورة
                            </label>
                            <textarea id="invoice_footer" name="invoice_footer" rows="3"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"><?php echo htmlspecialchars($invoiceSettings['footer'] ?? ''); ?></textarea>
                        </div>

                        <!-- خيارات إضافية -->
                        <div class="mt-6 space-y-4">
                            <div class="flex items-center">
                                <input type="checkbox" id="invoice_auto_number" name="invoice_auto_number" value="1"
                                       <?php echo ($invoiceSettings['auto_number'] ?? '1') === '1' ? 'checked' : ''; ?>
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="invoice_auto_number" class="mr-2 block text-sm text-gray-900">
                                    ترقيم تلقائي للفواتير
                                </label>
                            </div>
                        </div>

                        <!-- زر الحفظ -->
                        <div class="mt-6 flex justify-end">
                            <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-save ml-2"></i>
                                حفظ الإعدادات
                            </button>
                        </div>

                    </form>

                <?php endif; ?>

            </div>
        </div>

    </div>

    <!-- JavaScript -->
    <script>
        // معاينة تنسيق العملة
        function updateCurrencyPreview() {
            const symbol = document.getElementById('currency_symbol').value || 'ريال';
            const position = document.getElementById('currency_position').value;
            const decimalPlaces = parseInt(document.getElementById('currency_decimal_places').value) || 2;
            const thousandsSeparator = document.getElementById('currency_thousands_separator').value;
            const decimalSeparator = document.getElementById('currency_decimal_separator').value;

            let amount = 1234.56;
            let formattedAmount = amount.toFixed(decimalPlaces);

            // تطبيق فاصل الآلاف
            if (thousandsSeparator) {
                const parts = formattedAmount.split('.');
                parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, thousandsSeparator);
                formattedAmount = parts.join(decimalSeparator);
            } else {
                formattedAmount = formattedAmount.replace('.', decimalSeparator);
            }

            // تطبيق موضع رمز العملة
            let preview;
            if (position === 'before') {
                preview = symbol + ' ' + formattedAmount;
            } else {
                preview = formattedAmount + ' ' + symbol;
            }

            document.getElementById('currency-preview').textContent = preview;
        }

        // تحديث المعاينة عند تغيير أي حقل
        document.addEventListener('DOMContentLoaded', function() {
            if (document.getElementById('currency_symbol')) {
                updateCurrencyPreview();

                document.getElementById('currency_symbol').addEventListener('input', updateCurrencyPreview);
                document.getElementById('currency_position').addEventListener('change', updateCurrencyPreview);
                document.getElementById('currency_decimal_places').addEventListener('change', updateCurrencyPreview);
                document.getElementById('currency_thousands_separator').addEventListener('change', updateCurrencyPreview);
                document.getElementById('currency_decimal_separator').addEventListener('change', updateCurrencyPreview);
            }
        });
    </script>

</body>
</html>
