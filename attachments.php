<?php
/**
 * صفحة إدارة المرفقات
 * Attachments Management Page
 */

define('APP_INIT', true);
require_once 'includes/init.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('attachments_manage');

// إنشاء مثيل نموذج المرفقات
$attachmentModel = new Attachment();

// معالجة العمليات
$action = $_GET['action'] ?? 'list';
$entityType = $_GET['entity_type'] ?? '';
$entityId = $_GET['entity_id'] ?? '';
$attachmentId = $_GET['id'] ?? '';
$message = '';
$messageType = '';

// معالجة رفع ملف جديد
if ($action === 'upload' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    if (verifyCSRFToken($csrfToken)) {
        $entityType = sanitizeInput($_POST['entity_type'] ?? '');
        $entityId = (int)($_POST['entity_id'] ?? 0);
        $description = sanitizeInput($_POST['description'] ?? '');
        
        if (isset($_FILES['attachment_file']) && $entityType && $entityId) {
            $result = $attachmentModel->uploadFile($_FILES['attachment_file'], $entityType, $entityId, $description);
            
            if ($result['success']) {
                $message = $result['message'];
                $messageType = 'success';
            } else {
                $message = $result['message'];
                $messageType = 'error';
            }
        } else {
            $message = 'يرجى اختيار ملف وتحديد الكيان';
            $messageType = 'error';
        }
    } else {
        $message = 'طلب غير صالح';
        $messageType = 'error';
    }
}

// معالجة تحديث وصف المرفق
if ($action === 'update_description' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    if (verifyCSRFToken($csrfToken)) {
        $attachmentId = (int)($_POST['attachment_id'] ?? 0);
        $description = sanitizeInput($_POST['description'] ?? '');
        
        if ($attachmentId) {
            if ($attachmentModel->updateDescription($attachmentId, $description)) {
                $message = 'تم تحديث الوصف بنجاح';
                $messageType = 'success';
            } else {
                $message = 'فشل في تحديث الوصف';
                $messageType = 'error';
            }
        }
    } else {
        $message = 'طلب غير صالح';
        $messageType = 'error';
    }
}

// معالجة حذف مرفق
if ($action === 'delete' && $attachmentId && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    if (verifyCSRFToken($csrfToken)) {
        if ($attachmentModel->deleteAttachment($attachmentId)) {
            $message = 'تم حذف المرفق بنجاح';
            $messageType = 'success';
        } else {
            $message = 'فشل في حذف المرفق';
            $messageType = 'error';
        }
        $action = 'list';
    } else {
        $message = 'طلب غير صالح';
        $messageType = 'error';
    }
}

// الحصول على البيانات حسب العملية
$attachments = [];
$stats = [];
$searchTerm = $_GET['search'] ?? '';
$filterEntityType = $_GET['filter_entity_type'] ?? '';
$filterFileType = $_GET['filter_file_type'] ?? '';

if ($action === 'list') {
    if ($searchTerm || $filterEntityType || $filterFileType) {
        $attachments = $attachmentModel->searchAttachments($searchTerm, $filterEntityType, $filterFileType);
    } else {
        // الحصول على جميع المرفقات (مع تحديد عدد)
        $attachments = $attachmentModel->searchAttachments('', null, null, 100);
    }
    $stats = $attachmentModel->getAttachmentsStats();
} elseif ($action === 'entity' && $entityType && $entityId) {
    $attachments = $attachmentModel->getEntityAttachments($entityType, $entityId);
}

$pageTitle = 'إدارة المرفقات';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- خط عربي -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- أيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        
        .file-drop-zone {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 40px 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .file-drop-zone:hover {
            border-color: #3b82f6;
            background-color: #eff6ff;
        }
        
        .file-drop-zone.dragover {
            border-color: #3b82f6;
            background-color: #dbeafe;
        }
        
        .attachment-card {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            transition: all 0.2s;
        }
        
        .attachment-card:hover {
            border-color: #3b82f6;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .attachment-preview {
            width: 60px;
            height: 60px;
            border-radius: 6px;
            object-fit: cover;
            border: 1px solid #e5e7eb;
        }
        
        .stats-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #3b82f6;
        }
    </style>
</head>
<body class="bg-gray-50">

    <!-- شريط التنقل العلوي -->
    <nav class="gradient-bg shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="dashboard.php" class="flex-shrink-0 flex items-center text-white hover:text-gray-200">
                        <i class="fas fa-arrow-right text-xl ml-3"></i>
                        <span>العودة للوحة التحكم</span>
                    </a>
                </div>
                
                <div class="flex items-center">
                    <h1 class="text-white text-xl font-bold">
                        <i class="fas fa-paperclip ml-2"></i>
                        <?php echo $pageTitle; ?>
                    </h1>
                </div>
                
                <div class="flex items-center space-x-4 space-x-reverse">
                    <!-- مكون الإشعارات -->
                    <?php include 'includes/notification_widget.php'; ?>
                    
                    <div class="text-white text-sm">
                        <i class="fas fa-user ml-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </div>
                    <a href="logout.php" class="text-white hover:text-gray-200 transition-colors duration-200">
                        <i class="fas fa-sign-out-alt ml-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        
        <!-- عرض الرسائل -->
        <?php if (!empty($message)): ?>
            <div class="mb-6">
                <div class="<?php echo $messageType === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700'; ?> px-4 py-3 rounded">
                    <i class="fas <?php echo $messageType === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?> ml-2"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            </div>
        <?php endif; ?>
        
        <?php if ($action === 'list'): ?>
            
            <!-- إحصائيات المرفقات -->
            <?php if (!empty($stats)): ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    
                    <div class="stats-card">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-paperclip text-blue-500 text-2xl"></i>
                            </div>
                            <div class="mr-5">
                                <p class="text-sm font-medium text-gray-500">إجمالي المرفقات</p>
                                <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['total_attachments'] ?? 0); ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stats-card" style="border-left-color: #10b981;">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-hdd text-green-500 text-2xl"></i>
                            </div>
                            <div class="mr-5">
                                <p class="text-sm font-medium text-gray-500">الحجم الإجمالي</p>
                                <p class="text-2xl font-bold text-gray-900"><?php echo $attachmentModel->formatFileSize($stats['total_size'] ?? 0); ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stats-card" style="border-left-color: #f59e0b;">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-image text-yellow-500 text-2xl"></i>
                            </div>
                            <div class="mr-5">
                                <p class="text-sm font-medium text-gray-500">الصور</p>
                                <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['images_count'] ?? 0); ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stats-card" style="border-left-color: #ef4444;">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-file-alt text-red-500 text-2xl"></i>
                            </div>
                            <div class="mr-5">
                                <p class="text-sm font-medium text-gray-500">المستندات</p>
                                <p class="text-2xl font-bold text-gray-900"><?php echo number_format($stats['documents_count'] ?? 0); ?></p>
                            </div>
                        </div>
                    </div>
                    
                </div>
            <?php endif; ?>

            <!-- نموذج رفع ملف جديد -->
            <div class="bg-white shadow-lg rounded-lg mb-8">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-upload ml-2"></i>
                        رفع ملف جديد
                    </h3>
                </div>

                <div class="p-6">
                    <form method="POST" action="?action=upload" enctype="multipart/form-data">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">

                            <!-- نوع الكيان -->
                            <div>
                                <label for="entity_type" class="block text-sm font-medium text-gray-700 mb-2">
                                    نوع الكيان <span class="text-red-500">*</span>
                                </label>
                                <select id="entity_type" name="entity_type" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">اختر نوع الكيان</option>
                                    <option value="invoice">فاتورة</option>
                                    <option value="customer">عميل</option>
                                    <option value="supplier">مورد</option>
                                    <option value="item">صنف</option>
                                    <option value="user">مستخدم</option>
                                </select>
                            </div>

                            <!-- معرف الكيان -->
                            <div>
                                <label for="entity_id" class="block text-sm font-medium text-gray-700 mb-2">
                                    معرف الكيان <span class="text-red-500">*</span>
                                </label>
                                <input type="number" id="entity_id" name="entity_id" required min="1"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>

                            <!-- وصف الملف -->
                            <div>
                                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                                    وصف الملف
                                </label>
                                <input type="text" id="description" name="description"
                                       placeholder="وصف اختياري للملف"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>

                        </div>

                        <!-- منطقة رفع الملف -->
                        <div class="file-drop-zone mb-6" onclick="document.getElementById('attachment_file').click()">
                            <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                            <p class="text-lg font-medium text-gray-700 mb-2">اختر ملف أو اسحبه هنا</p>
                            <p class="text-sm text-gray-500">الحد الأقصى: 10 ميجابايت</p>
                            <p class="text-xs text-gray-400 mt-2">الأنواع المدعومة: JPG, PNG, PDF, DOC, XLS, ZIP</p>
                            <input type="file" id="attachment_file" name="attachment_file" required class="hidden">
                        </div>

                        <!-- زر الرفع -->
                        <div class="text-center">
                            <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-8 py-3 rounded-lg transition-colors duration-200">
                                <i class="fas fa-upload ml-2"></i>
                                رفع الملف
                            </button>
                        </div>

                    </form>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="bg-white shadow-lg rounded-lg mb-8">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-search ml-2"></i>
                        البحث والفلترة
                    </h3>
                </div>

                <div class="p-6">
                    <form method="GET" action="">
                        <input type="hidden" name="action" value="list">

                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">

                            <!-- البحث -->
                            <div>
                                <label for="search" class="block text-sm font-medium text-gray-700 mb-2">
                                    البحث في الملفات
                                </label>
                                <input type="text" id="search" name="search" value="<?php echo htmlspecialchars($searchTerm); ?>"
                                       placeholder="اسم الملف أو الوصف"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>

                            <!-- فلتر نوع الكيان -->
                            <div>
                                <label for="filter_entity_type" class="block text-sm font-medium text-gray-700 mb-2">
                                    نوع الكيان
                                </label>
                                <select id="filter_entity_type" name="filter_entity_type"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">جميع الأنواع</option>
                                    <option value="invoice" <?php echo $filterEntityType === 'invoice' ? 'selected' : ''; ?>>فاتورة</option>
                                    <option value="customer" <?php echo $filterEntityType === 'customer' ? 'selected' : ''; ?>>عميل</option>
                                    <option value="supplier" <?php echo $filterEntityType === 'supplier' ? 'selected' : ''; ?>>مورد</option>
                                    <option value="item" <?php echo $filterEntityType === 'item' ? 'selected' : ''; ?>>صنف</option>
                                    <option value="user" <?php echo $filterEntityType === 'user' ? 'selected' : ''; ?>>مستخدم</option>
                                </select>
                            </div>

                            <!-- فلتر نوع الملف -->
                            <div>
                                <label for="filter_file_type" class="block text-sm font-medium text-gray-700 mb-2">
                                    نوع الملف
                                </label>
                                <select id="filter_file_type" name="filter_file_type"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">جميع الأنواع</option>
                                    <option value="image" <?php echo $filterFileType === 'image' ? 'selected' : ''; ?>>صور</option>
                                    <option value="document" <?php echo $filterFileType === 'document' ? 'selected' : ''; ?>>مستندات</option>
                                    <option value="archive" <?php echo $filterFileType === 'archive' ? 'selected' : ''; ?>>أرشيف</option>
                                    <option value="other" <?php echo $filterFileType === 'other' ? 'selected' : ''; ?>>أخرى</option>
                                </select>
                            </div>

                            <!-- زر البحث -->
                            <div class="flex items-end">
                                <button type="submit" class="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                                    <i class="fas fa-search ml-2"></i>
                                    بحث
                                </button>
                            </div>

                        </div>
                    </form>
                </div>
            </div>

            <!-- قائمة المرفقات -->
            <div class="bg-white shadow-lg rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-list ml-2"></i>
                        المرفقات (<?php echo count($attachments); ?> ملف)
                    </h3>
                </div>

                <div class="p-6">
                    <?php if (empty($attachments)): ?>
                        <div class="text-center py-12">
                            <i class="fas fa-paperclip text-gray-400 text-6xl mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد مرفقات</h3>
                            <p class="text-gray-500">لم يتم العثور على أي مرفقات</p>
                        </div>
                    <?php else: ?>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <?php foreach ($attachments as $attachment): ?>
                                <div class="attachment-card">
                                    <div class="flex items-start space-x-3 space-x-reverse">

                                        <!-- معاينة الملف -->
                                        <div class="flex-shrink-0">
                                            <?php if ($attachment['file_type'] === 'image'): ?>
                                                <img src="<?php echo htmlspecialchars($attachment['file_path']); ?>"
                                                     alt="<?php echo htmlspecialchars($attachment['original_name']); ?>"
                                                     class="attachment-preview">
                                            <?php else: ?>
                                                <div class="attachment-preview flex items-center justify-center bg-gray-100">
                                                    <i class="<?php echo $attachmentModel->getFileIcon($attachment['file_type'], $attachment['original_name']); ?> text-2xl"></i>
                                                </div>
                                            <?php endif; ?>
                                        </div>

                                        <!-- معلومات الملف -->
                                        <div class="flex-1 min-w-0">
                                            <h4 class="text-sm font-medium text-gray-900 truncate" title="<?php echo htmlspecialchars($attachment['original_name']); ?>">
                                                <?php echo htmlspecialchars($attachment['original_name']); ?>
                                            </h4>

                                            <?php if (!empty($attachment['description'])): ?>
                                                <p class="text-sm text-gray-600 mt-1"><?php echo htmlspecialchars($attachment['description']); ?></p>
                                            <?php endif; ?>

                                            <div class="flex items-center mt-2 text-xs text-gray-500">
                                                <span><?php echo $attachmentModel->formatFileSize($attachment['file_size']); ?></span>
                                                <span class="mx-2">•</span>
                                                <span><?php echo ucfirst($attachment['entity_type']); ?> #<?php echo $attachment['entity_id']; ?></span>
                                            </div>

                                            <div class="flex items-center mt-1 text-xs text-gray-400">
                                                <i class="fas fa-user ml-1"></i>
                                                <span><?php echo htmlspecialchars($attachment['uploaded_by_name'] ?? 'غير محدد'); ?></span>
                                                <span class="mx-2">•</span>
                                                <span><?php echo formatDateTime($attachment['created_at']); ?></span>
                                            </div>
                                        </div>

                                    </div>

                                    <!-- أزرار الإجراءات -->
                                    <div class="flex justify-between items-center mt-4 pt-4 border-t border-gray-200">
                                        <div class="flex space-x-2 space-x-reverse">
                                            <!-- تحميل -->
                                            <a href="download_attachment.php?id=<?php echo $attachment['attachment_id']; ?>"
                                               class="text-blue-600 hover:text-blue-800 text-sm" title="تحميل">
                                                <i class="fas fa-download"></i>
                                            </a>

                                            <!-- معاينة -->
                                            <a href="view_attachment.php?id=<?php echo $attachment['attachment_id']; ?>"
                                               target="_blank" class="text-green-600 hover:text-green-800 text-sm" title="معاينة">
                                                <i class="fas fa-eye"></i>
                                            </a>

                                            <!-- تحرير الوصف -->
                                            <button onclick="editDescription(<?php echo $attachment['attachment_id']; ?>, '<?php echo htmlspecialchars($attachment['description']); ?>')"
                                                    class="text-yellow-600 hover:text-yellow-800 text-sm" title="تحرير الوصف">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        </div>

                                        <!-- حذف -->
                                        <form method="POST" action="?action=delete&id=<?php echo $attachment['attachment_id']; ?>" class="inline">
                                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                            <button type="submit" onclick="return confirm('هل أنت متأكد من حذف هذا المرفق؟')"
                                                    class="text-red-600 hover:text-red-800 text-sm" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

        <?php elseif ($action === 'entity' && $entityType && $entityId): ?>

            <!-- مرفقات كيان محدد -->
            <div class="bg-white shadow-lg rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-paperclip ml-2"></i>
                        مرفقات <?php echo ucfirst($entityType); ?> #<?php echo $entityId; ?>
                    </h3>
                </div>

                <div class="p-6">
                    <!-- نموذج رفع سريع -->
                    <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                        <form method="POST" action="?action=upload" enctype="multipart/form-data" class="flex items-end gap-4">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <input type="hidden" name="entity_type" value="<?php echo htmlspecialchars($entityType); ?>">
                            <input type="hidden" name="entity_id" value="<?php echo htmlspecialchars($entityId); ?>">

                            <div class="flex-1">
                                <label for="quick_file" class="block text-sm font-medium text-gray-700 mb-2">
                                    رفع ملف جديد
                                </label>
                                <input type="file" id="quick_file" name="attachment_file" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>

                            <div class="flex-1">
                                <label for="quick_description" class="block text-sm font-medium text-gray-700 mb-2">
                                    الوصف
                                </label>
                                <input type="text" id="quick_description" name="description"
                                       placeholder="وصف اختياري"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>

                            <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-upload ml-2"></i>
                                رفع
                            </button>
                        </form>
                    </div>

                    <!-- قائمة المرفقات -->
                    <?php if (empty($attachments)): ?>
                        <div class="text-center py-8">
                            <i class="fas fa-paperclip text-gray-400 text-4xl mb-4"></i>
                            <p class="text-gray-500">لا توجد مرفقات لهذا الكيان</p>
                        </div>
                    <?php else: ?>
                        <div class="space-y-4">
                            <?php foreach ($attachments as $attachment): ?>
                                <div class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                                    <div class="flex-shrink-0 ml-4">
                                        <i class="<?php echo $attachmentModel->getFileIcon($attachment['file_type'], $attachment['original_name']); ?> text-2xl"></i>
                                    </div>

                                    <div class="flex-1">
                                        <h4 class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($attachment['original_name']); ?></h4>
                                        <?php if (!empty($attachment['description'])): ?>
                                            <p class="text-sm text-gray-600"><?php echo htmlspecialchars($attachment['description']); ?></p>
                                        <?php endif; ?>
                                        <div class="text-xs text-gray-500 mt-1">
                                            <?php echo $attachmentModel->formatFileSize($attachment['file_size']); ?> •
                                            <?php echo formatDateTime($attachment['created_at']); ?>
                                        </div>
                                    </div>

                                    <div class="flex space-x-2 space-x-reverse">
                                        <a href="download_attachment.php?id=<?php echo $attachment['attachment_id']; ?>"
                                           class="text-blue-600 hover:text-blue-800" title="تحميل">
                                            <i class="fas fa-download"></i>
                                        </a>
                                        <a href="view_attachment.php?id=<?php echo $attachment['attachment_id']; ?>"
                                           target="_blank" class="text-green-600 hover:text-green-800" title="معاينة">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <form method="POST" action="?action=delete&id=<?php echo $attachment['attachment_id']; ?>" class="inline">
                                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                            <button type="submit" onclick="return confirm('هل أنت متأكد من حذف هذا المرفق؟')"
                                                    class="text-red-600 hover:text-red-800" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

        <?php endif; ?>

    </div>

    <!-- نافذة تحرير الوصف -->
    <div id="editDescriptionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">تحرير وصف المرفق</h3>
                </div>

                <form method="POST" action="?action=update_description">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" id="edit_attachment_id" name="attachment_id" value="">

                    <div class="p-6">
                        <label for="edit_description" class="block text-sm font-medium text-gray-700 mb-2">
                            الوصف
                        </label>
                        <textarea id="edit_description" name="description" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                    </div>

                    <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3 space-x-reverse">
                        <button type="button" onclick="closeEditModal()"
                                class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg transition-colors duration-200">
                            إلغاء
                        </button>
                        <button type="submit"
                                class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            حفظ
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // تحرير وصف المرفق
        function editDescription(attachmentId, currentDescription) {
            document.getElementById('edit_attachment_id').value = attachmentId;
            document.getElementById('edit_description').value = currentDescription;
            document.getElementById('editDescriptionModal').classList.remove('hidden');
        }

        // إغلاق نافذة التحرير
        function closeEditModal() {
            document.getElementById('editDescriptionModal').classList.add('hidden');
        }

        // معاينة اسم الملف المختار
        document.getElementById('attachment_file').addEventListener('change', function() {
            const fileName = this.files[0]?.name || '';
            if (fileName) {
                const dropZone = document.querySelector('.file-drop-zone');
                dropZone.innerHTML = `
                    <i class="fas fa-file text-4xl text-blue-500 mb-4"></i>
                    <p class="text-lg font-medium text-gray-700 mb-2">تم اختيار: ${fileName}</p>
                    <p class="text-sm text-gray-500">انقر لاختيار ملف آخر</p>
                `;
            }
        });

        // إغلاق النافذة عند النقر خارجها
        document.getElementById('editDescriptionModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeEditModal();
            }
        });
    </script>

</body>
</html>
