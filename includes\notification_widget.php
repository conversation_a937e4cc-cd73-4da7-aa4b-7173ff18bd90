<?php
/**
 * مكون الإشعارات للشريط العلوي
 * Notification Widget for Top Navigation
 */

// التأكد من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    return;
}

// إنشاء مثيل نموذج الإشعارات
$notificationModel = new Notification();

// الحصول على الإشعارات غير المقروءة (آخر 10)
$unreadNotifications = $notificationModel->getUserNotifications($_SESSION['user_id'], true, 10);
$unreadCount = $notificationModel->getUnreadCount($_SESSION['user_id']);
?>

<!-- مكون الإشعارات -->
<div class="relative">
    <!-- زر الإشعارات -->
    <button id="notifications-button" 
            class="relative p-2 text-white hover:text-gray-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-gray-800 rounded-full">
        <i class="fas fa-bell text-lg"></i>
        <?php if ($unreadCount > 0): ?>
            <span class="absolute -top-1 -right-1 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-500 rounded-full min-w-[1.25rem] h-5">
                <?php echo $unreadCount > 99 ? '99+' : $unreadCount; ?>
            </span>
        <?php endif; ?>
    </button>
    
    <!-- قائمة الإشعارات المنسدلة -->
    <div id="notifications-dropdown" 
         class="hidden absolute left-0 mt-2 w-80 bg-white rounded-lg shadow-lg ring-1 ring-black ring-opacity-5 z-50">
        
        <!-- رأس القائمة -->
        <div class="px-4 py-3 border-b border-gray-200 flex justify-between items-center">
            <h3 class="text-sm font-medium text-gray-900">الإشعارات</h3>
            <?php if ($unreadCount > 0): ?>
                <a href="notifications.php?action=mark_all_read" 
                   class="text-xs text-blue-600 hover:text-blue-800">
                    تحديد الكل كمقروء
                </a>
            <?php endif; ?>
        </div>
        
        <!-- قائمة الإشعارات -->
        <div class="max-h-96 overflow-y-auto">
            <?php if (empty($unreadNotifications)): ?>
                <div class="px-4 py-6 text-center">
                    <i class="fas fa-bell-slash text-gray-400 text-2xl mb-2"></i>
                    <p class="text-sm text-gray-500">لا توجد إشعارات جديدة</p>
                </div>
            <?php else: ?>
                <?php foreach ($unreadNotifications as $notification): ?>
                    <?php
                    // تحديد أيقونة الإشعار
                    $typeIcons = [
                        'low_stock' => ['icon' => 'fas fa-box', 'color' => 'text-red-500'],
                        'overdue_invoice' => ['icon' => 'fas fa-file-invoice-dollar', 'color' => 'text-orange-500'],
                        'backup_warning' => ['icon' => 'fas fa-exclamation-triangle', 'color' => 'text-purple-500'],
                        'no_backup' => ['icon' => 'fas fa-database', 'color' => 'text-red-600'],
                        'system' => ['icon' => 'fas fa-cog', 'color' => 'text-gray-500']
                    ];
                    
                    $iconData = $typeIcons[$notification['notification_type']] ?? ['icon' => 'fas fa-bell', 'color' => 'text-blue-500'];
                    
                    // تحديد لون الأولوية
                    $priorityColors = [
                        'high' => 'border-r-red-500',
                        'medium' => 'border-r-orange-500',
                        'low' => 'border-r-green-500'
                    ];
                    
                    $priorityColor = $priorityColors[$notification['priority']] ?? 'border-r-blue-500';
                    ?>
                    
                    <div class="px-4 py-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer border-r-4 <?php echo $priorityColor; ?>"
                         onclick="<?php echo !empty($notification['action_url']) ? "window.location.href='{$notification['action_url']}'" : 'markNotificationAsRead(' . $notification['notification_id'] . ')'; ?>">
                        <div class="flex items-start space-x-3 space-x-reverse">
                            <div class="flex-shrink-0">
                                <i class="<?php echo $iconData['icon']; ?> <?php echo $iconData['color']; ?>"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 truncate">
                                    <?php echo htmlspecialchars($notification['title']); ?>
                                </p>
                                <p class="text-xs text-gray-600 mt-1 line-clamp-2">
                                    <?php echo htmlspecialchars($notification['message']); ?>
                                </p>
                                <p class="text-xs text-gray-400 mt-1">
                                    <?php 
                                    $timeAgo = time() - strtotime($notification['created_at']);
                                    if ($timeAgo < 60) {
                                        echo 'الآن';
                                    } elseif ($timeAgo < 3600) {
                                        echo floor($timeAgo / 60) . ' دقيقة';
                                    } elseif ($timeAgo < 86400) {
                                        echo floor($timeAgo / 3600) . ' ساعة';
                                    } else {
                                        echo formatDate($notification['created_at']);
                                    }
                                    ?>
                                </p>
                            </div>
                        </div>
                    </div>
                    
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
        
        <!-- تذييل القائمة -->
        <div class="px-4 py-3 border-t border-gray-200 bg-gray-50">
            <a href="notifications.php" 
               class="block text-center text-sm text-blue-600 hover:text-blue-800 font-medium">
                عرض جميع الإشعارات
            </a>
        </div>
        
    </div>
</div>

<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>

<script>
// إدارة قائمة الإشعارات المنسدلة
document.addEventListener('DOMContentLoaded', function() {
    const notificationsButton = document.getElementById('notifications-button');
    const notificationsDropdown = document.getElementById('notifications-dropdown');
    
    if (notificationsButton && notificationsDropdown) {
        // فتح/إغلاق القائمة
        notificationsButton.addEventListener('click', function(e) {
            e.stopPropagation();
            notificationsDropdown.classList.toggle('hidden');
        });
        
        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', function(e) {
            if (!notificationsButton.contains(e.target) && !notificationsDropdown.contains(e.target)) {
                notificationsDropdown.classList.add('hidden');
            }
        });
        
        // منع إغلاق القائمة عند النقر داخلها
        notificationsDropdown.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }
});

// تحديد إشعار كمقروء
function markNotificationAsRead(notificationId) {
    fetch(`notifications.php?action=mark_read&id=${notificationId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // إعادة تحميل الصفحة لتحديث عداد الإشعارات
                location.reload();
            }
        })
        .catch(error => {
            console.error('Error marking notification as read:', error);
        });
}

// تحديث الإشعارات تلقائياً كل 5 دقائق
setInterval(function() {
    // يمكن إضافة AJAX لتحديث الإشعارات دون إعادة تحميل الصفحة
    // fetch('get_notifications.php')...
}, 300000); // 5 دقائق
</script>
