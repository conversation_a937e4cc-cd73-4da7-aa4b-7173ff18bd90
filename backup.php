<?php
/**
 * صفحة إدارة النسخ الاحتياطي
 * Backup Management Page
 */

define('APP_INIT', true);
require_once 'includes/init.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('system_backup');

// إنشاء مثيل نموذج النسخ الاحتياطي
$backupModel = new Backup();

// معالجة العمليات
$action = $_GET['action'] ?? 'list';
$backupId = $_GET['id'] ?? null;

// معالجة إنشاء نسخة احتياطية
if ($action === 'create' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    if (verifyCSRFToken($csrfToken)) {
        $backupType = sanitizeInput($_POST['backup_type'] ?? '');
        $description = sanitizeInput($_POST['description'] ?? '');
        
        $result = null;
        
        switch ($backupType) {
            case 'database':
                $result = $backupModel->createDatabaseBackup($description);
                break;
            case 'files':
                $result = $backupModel->createFilesBackup($description);
                break;
            case 'full':
                $result = $backupModel->createFullBackup($description);
                break;
            default:
                setAlert('نوع النسخة الاحتياطية غير صحيح', 'error');
                redirect('backup.php');
        }
        
        if ($result && $result['success']) {
            setAlert('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
        } else {
            $error = $result['error'] ?? 'فشل في إنشاء النسخة الاحتياطية';
            setAlert($error, 'error');
        }
    } else {
        setAlert('طلب غير صالح', 'error');
    }
    
    redirect('backup.php');
}

// معالجة التحميل
if ($action === 'download' && $backupId) {
    $backup = $backupModel->getBackupById($backupId);
    
    if ($backup && $backup['status'] === 'completed' && file_exists($backup['filepath'])) {
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $backup['filename'] . '"');
        header('Content-Length: ' . filesize($backup['filepath']));
        readfile($backup['filepath']);
        
        logActivity('تحميل نسخة احتياطية', "تم تحميل النسخة الاحتياطية: {$backup['filename']}");
        exit;
    } else {
        setAlert('الملف غير موجود أو تالف', 'error');
        redirect('backup.php');
    }
}

// معالجة الحذف
if ($action === 'delete' && $backupId) {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $csrfToken = $_POST['csrf_token'] ?? '';
        
        if (verifyCSRFToken($csrfToken)) {
            try {
                if ($backupModel->deleteBackup($backupId)) {
                    setAlert('تم حذف النسخة الاحتياطية بنجاح', 'success');
                } else {
                    setAlert('فشل في حذف النسخة الاحتياطية', 'error');
                }
            } catch (Exception $e) {
                setAlert($e->getMessage(), 'error');
            }
        } else {
            setAlert('طلب غير صالح', 'error');
        }
        
        redirect('backup.php');
    }
}

// معالجة تنظيف النسخ القديمة
if ($action === 'clean' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    if (verifyCSRFToken($csrfToken)) {
        $daysToKeep = (int)($_POST['days_to_keep'] ?? 30);
        
        if ($daysToKeep < 7) {
            setAlert('يجب الاحتفاظ بالنسخ لمدة 7 أيام على الأقل', 'error');
        } else {
            $deletedCount = $backupModel->cleanOldBackups($daysToKeep);
            
            if ($deletedCount !== false) {
                setAlert("تم حذف {$deletedCount} نسخة احتياطية قديمة", 'success');
            } else {
                setAlert('فشل في تنظيف النسخ القديمة', 'error');
            }
        }
    } else {
        setAlert('طلب غير صالح', 'error');
    }
    
    redirect('backup.php');
}

// إعداد الفلترة والبحث
$filters = [
    'backup_type' => sanitizeInput($_GET['backup_type'] ?? ''),
    'status' => sanitizeInput($_GET['status'] ?? ''),
    'date_from' => sanitizeInput($_GET['date_from'] ?? ''),
    'date_to' => sanitizeInput($_GET['date_to'] ?? ''),
    'limit' => 20,
    'offset' => ((int)($_GET['page'] ?? 1) - 1) * 20
];

// الحصول على البيانات
$backups = $backupModel->getBackups($filters);
$backupStats = $backupModel->getBackupStats();

// حساب الصفحات
$currentPage = (int)($_GET['page'] ?? 1);
$totalBackups = count($backupModel->getBackups(array_merge($filters, ['limit' => 1000, 'offset' => 0])));
$totalPages = ceil($totalBackups / 20);

$pageTitle = 'إدارة النسخ الاحتياطي';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- خط عربي -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- أيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .card-hover:hover { transform: translateY(-2px); transition: all 0.3s ease; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        
        /* تحسين عرض النسخ الاحتياطية */
        .backup-item {
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            border-right: 4px solid transparent;
        }
        
        .backup-database { border-right-color: #3b82f6; }
        .backup-files { border-right-color: #10b981; }
        .backup-full { border-right-color: #8b5cf6; }
        
        .status-completed { background-color: #d1fae5; color: #065f46; }
        .status-failed { background-color: #fee2e2; color: #991b1b; }
        .status-running { background-color: #fef3c7; color: #92400e; }
        
        .backup-icon {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            color: white;
        }
        
        .icon-database { background-color: #3b82f6; }
        .icon-files { background-color: #10b981; }
        .icon-full { background-color: #8b5cf6; }
    </style>
</head>
<body class="bg-gray-50">

    <!-- شريط التنقل العلوي -->
    <nav class="gradient-bg shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="dashboard.php" class="flex-shrink-0 flex items-center text-white hover:text-gray-200">
                        <i class="fas fa-arrow-right text-xl ml-3"></i>
                        <span>العودة للوحة التحكم</span>
                    </a>
                </div>
                
                <div class="flex items-center">
                    <h1 class="text-white text-xl font-bold">
                        <i class="fas fa-database ml-2"></i>
                        <?php echo $pageTitle; ?>
                    </h1>
                </div>
                
                <div class="flex items-center space-x-4 space-x-reverse">
                    <span class="text-white text-sm">
                        <i class="fas fa-user ml-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </span>
                    <a href="logout.php" class="text-white hover:text-gray-200 transition-colors duration-200">
                        <i class="fas fa-sign-out-alt ml-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        
        <!-- عرض الرسائل -->
        <?php showAlert(); ?>
        
        <!-- بطاقات الإحصائيات -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            
            <!-- إجمالي النسخ -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-database text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">إجمالي النسخ</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?php echo number_format($backupStats['general']['total_backups']); ?>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- النسخ الناجحة -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-check-circle text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">النسخ الناجحة</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?php echo number_format($backupStats['general']['successful_backups']); ?>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- نسخ اليوم -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-calendar-day text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">نسخ اليوم</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?php echo number_format($backupStats['general']['today_backups']); ?>
                                </dd>
                            </dl>
                        </div>

        <!-- إنشاء نسخة احتياطية جديدة -->
        <div class="bg-white shadow-lg rounded-lg mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">
                    <i class="fas fa-plus ml-2"></i>
                    إنشاء نسخة احتياطية جديدة
                </h3>
            </div>

            <div class="p-6">
                <form method="POST" action="backup.php?action=create" class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                    <!-- نوع النسخة الاحتياطية -->
                    <div>
                        <label for="backup_type" class="block text-sm font-medium text-gray-700 mb-2">
                            نوع النسخة الاحتياطية <span class="text-red-500">*</span>
                        </label>
                        <select
                            id="backup_type"
                            name="backup_type"
                            required
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            <option value="">اختر نوع النسخة</option>
                            <option value="database">قاعدة البيانات فقط</option>
                            <option value="files">الملفات فقط</option>
                            <option value="full">نسخة كاملة (قاعدة البيانات + الملفات)</option>
                        </select>
                    </div>

                    <!-- الوصف -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                            الوصف (اختياري)
                        </label>
                        <input
                            type="text"
                            id="description"
                            name="description"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="وصف مختصر للنسخة الاحتياطية"
                        >
                    </div>

                    <!-- زر الإنشاء -->
                    <div class="pt-6">
                        <button type="submit" class="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-plus ml-2"></i>
                            إنشاء النسخة الاحتياطية
                        </button>
                    </div>

                </form>

                <!-- معلومات مهمة -->
                <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex">
                        <i class="fas fa-info-circle text-blue-400 ml-2 mt-1"></i>
                        <div class="text-sm text-blue-700">
                            <strong>ملاحظات مهمة:</strong>
                            <ul class="list-disc list-inside mt-2 space-y-1">
                                <li><strong>قاعدة البيانات:</strong> تشمل جميع البيانات والجداول</li>
                                <li><strong>الملفات:</strong> تشمل الملفات المرفوعة وملفات الإعدادات</li>
                                <li><strong>النسخة الكاملة:</strong> تشمل قاعدة البيانات والملفات معاً</li>
                                <li>يتم ضغط النسخ الاحتياطية تلقائياً لتوفير المساحة</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قائمة النسخ الاحتياطية -->
        <div class="bg-white shadow-lg rounded-lg">

            <!-- رأس القائمة -->
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900">
                    <i class="fas fa-list ml-2"></i>
                    النسخ الاحتياطية (<?php echo count($backups); ?> نسخة)
                </h3>
                <div class="flex space-x-2 space-x-reverse">
                    <button onclick="showCleanModal()" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                        <i class="fas fa-trash ml-2"></i>
                        تنظيف النسخ القديمة
                    </button>
                </div>
            </div>

            <!-- شريط الفلترة -->
            <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
                <form method="GET" action="backup.php" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">

                    <!-- نوع النسخة -->
                    <div>
                        <select name="backup_type" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">جميع الأنواع</option>
                            <option value="database" <?php echo $filters['backup_type'] === 'database' ? 'selected' : ''; ?>>قاعدة البيانات</option>
                            <option value="files" <?php echo $filters['backup_type'] === 'files' ? 'selected' : ''; ?>>الملفات</option>
                            <option value="full" <?php echo $filters['backup_type'] === 'full' ? 'selected' : ''; ?>>نسخة كاملة</option>
                        </select>
                    </div>

                    <!-- الحالة -->
                    <div>
                        <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">جميع الحالات</option>
                            <option value="completed" <?php echo $filters['status'] === 'completed' ? 'selected' : ''; ?>>مكتملة</option>
                            <option value="failed" <?php echo $filters['status'] === 'failed' ? 'selected' : ''; ?>>فاشلة</option>
                        </select>
                    </div>

                    <!-- من تاريخ -->
                    <div>
                        <input
                            type="date"
                            name="date_from"
                            value="<?php echo htmlspecialchars($filters['date_from']); ?>"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                    </div>

                    <!-- إلى تاريخ -->
                    <div>
                        <input
                            type="date"
                            name="date_to"
                            value="<?php echo htmlspecialchars($filters['date_to']); ?>"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                    </div>

                    <!-- أزرار البحث -->
                    <div class="lg:col-span-4 flex space-x-2 space-x-reverse">
                        <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-search ml-2"></i>
                            بحث
                        </button>
                        <a href="backup.php" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-times ml-2"></i>
                            مسح الفلاتر
                        </a>
                    </div>

                </form>
            </div>

            <!-- قائمة النسخ -->
            <div class="p-6">
                <?php if (empty($backups)): ?>
                    <div class="text-center py-12">
                        <i class="fas fa-database text-gray-400 text-6xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد نسخ احتياطية</h3>
                        <p class="text-gray-500">لم يتم إنشاء أي نسخ احتياطية بعد</p>
                    </div>
                <?php else: ?>
                    <div class="space-y-4">
                        <?php foreach ($backups as $backup): ?>
                            <?php
                            // تحديد نوع النسخة للتصميم
                            $backupTypeClass = 'backup-' . $backup['backup_type'];
                            $iconClass = 'icon-' . $backup['backup_type'];

                            $typeLabels = [
                                'database' => 'قاعدة البيانات',
                                'files' => 'الملفات',
                                'full' => 'نسخة كاملة'
                            ];

                            $typeIcons = [
                                'database' => 'fas fa-database',
                                'files' => 'fas fa-folder',
                                'full' => 'fas fa-archive'
                            ];
                            ?>

                            <div class="backup-item <?php echo $backupTypeClass; ?> bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                                <div class="flex items-center justify-between">

                                    <!-- معلومات النسخة -->
                                    <div class="flex items-center space-x-4 space-x-reverse">

                                        <!-- أيقونة النوع -->
                                        <div class="backup-icon <?php echo $iconClass; ?>">
                                            <i class="<?php echo $typeIcons[$backup['backup_type']]; ?>"></i>
                                        </div>

                                        <!-- التفاصيل -->
                                        <div>
                                            <div class="flex items-center space-x-2 space-x-reverse">
                                                <h4 class="text-sm font-medium text-gray-900">
                                                    <?php echo htmlspecialchars($backup['filename']); ?>
                                                </h4>
                                                <span class="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">
                                                    <?php echo $typeLabels[$backup['backup_type']]; ?>
                                                </span>
                                                <span class="px-2 py-1 text-xs rounded status-<?php echo $backup['status']; ?>">
                                                    <?php echo $backup['status'] === 'completed' ? 'مكتملة' : ($backup['status'] === 'failed' ? 'فاشلة' : 'قيد التنفيذ'); ?>
                                                </span>
                                            </div>

                                            <?php if (!empty($backup['description'])): ?>
                                                <p class="mt-1 text-sm text-gray-600">
                                                    <?php echo htmlspecialchars($backup['description']); ?>
                                                </p>
                                            <?php endif; ?>

                                            <div class="mt-2 flex items-center space-x-4 space-x-reverse text-xs text-gray-500">
                                                <div class="flex items-center">
                                                    <i class="fas fa-calendar ml-1"></i>
                                                    <?php echo formatDate($backup['created_at']); ?>
                                                    <span class="mr-2"><?php echo date('H:i', strtotime($backup['created_at'])); ?></span>
                                                </div>
                                                <?php if ($backup['status'] === 'completed' && !empty($backup['file_size'])): ?>
                                                    <div class="flex items-center">
                                                        <i class="fas fa-hdd ml-1"></i>
                                                        <?php echo formatFileSize($backup['file_size']); ?>
                                                    </div>
                                                <?php endif; ?>
                                                <?php if (!empty($backup['created_by_name'])): ?>
                                                    <div class="flex items-center">
                                                        <i class="fas fa-user ml-1"></i>
                                                        <?php echo htmlspecialchars($backup['created_by_name']); ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>

                                            <?php if ($backup['status'] === 'failed' && !empty($backup['error_message'])): ?>
                                                <div class="mt-2 text-xs text-red-600">
                                                    <i class="fas fa-exclamation-triangle ml-1"></i>
                                                    <?php echo htmlspecialchars($backup['error_message']); ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>

                                    </div>

                                    <!-- الإجراءات -->
                                    <div class="flex space-x-2 space-x-reverse">
                                        <?php if ($backup['status'] === 'completed'): ?>
                                            <a href="backup.php?action=download&id=<?php echo $backup['backup_id']; ?>"
                                               class="text-blue-600 hover:text-blue-900 transition-colors duration-200"
                                               title="تحميل">
                                                <i class="fas fa-download"></i>
                                            </a>
                                        <?php endif; ?>
                                        <a href="backup.php?action=delete&id=<?php echo $backup['backup_id']; ?>"
                                           onclick="return confirm('هل أنت متأكد من حذف النسخة الاحتياطية \'<?php echo htmlspecialchars($backup['filename'], ENT_QUOTES); ?>\'؟')"
                                           class="text-red-600 hover:text-red-900 transition-colors duration-200"
                                           title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>

                                </div>
                            </div>

                        <?php endforeach; ?>
                    </div>

                    <!-- التنقل بين الصفحات -->
                    <?php if ($totalPages > 1): ?>
                        <div class="mt-8 flex justify-center">
                            <nav class="flex space-x-2 space-x-reverse">

                                <!-- الصفحة السابقة -->
                                <?php if ($currentPage > 1): ?>
                                    <a href="backup.php?page=<?php echo $currentPage - 1; ?>&<?php echo http_build_query(array_filter($filters)); ?>"
                                       class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                                        <i class="fas fa-chevron-right ml-1"></i>
                                        السابق
                                    </a>
                                <?php endif; ?>

                                <!-- أرقام الصفحات -->
                                <?php
                                $startPage = max(1, $currentPage - 2);
                                $endPage = min($totalPages, $currentPage + 2);
                                ?>

                                <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                                    <?php if ($i == $currentPage): ?>
                                        <span class="px-3 py-2 text-sm bg-blue-500 text-white rounded-lg">
                                            <?php echo $i; ?>
                                        </span>
                                    <?php else: ?>
                                        <a href="backup.php?page=<?php echo $i; ?>&<?php echo http_build_query(array_filter($filters)); ?>"
                                           class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                                            <?php echo $i; ?>
                                        </a>
                                    <?php endif; ?>
                                <?php endfor; ?>

                                <!-- الصفحة التالية -->
                                <?php if ($currentPage < $totalPages): ?>
                                    <a href="backup.php?page=<?php echo $currentPage + 1; ?>&<?php echo http_build_query(array_filter($filters)); ?>"
                                       class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                                        التالي
                                        <i class="fas fa-chevron-left mr-1"></i>
                                    </a>
                                <?php endif; ?>

                            </nav>
                        </div>
                    <?php endif; ?>

                <?php endif; ?>
            </div>

        </div>

    </div>

    <!-- نافذة تنظيف النسخ القديمة -->
    <div id="cleanModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">تنظيف النسخ القديمة</h3>
                    <button onclick="hideCleanModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <form method="POST" action="backup.php?action=clean">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                    <div class="mb-4">
                        <label for="days_to_keep" class="block text-sm font-medium text-gray-700 mb-2">
                            الاحتفاظ بالنسخ لمدة (بالأيام)
                        </label>
                        <input
                            type="number"
                            id="days_to_keep"
                            name="days_to_keep"
                            value="30"
                            min="7"
                            max="365"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            required
                        >
                        <p class="text-xs text-gray-500 mt-1">الحد الأدنى: 7 أيام</p>
                    </div>

                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
                        <div class="flex">
                            <i class="fas fa-exclamation-triangle text-yellow-400 ml-2"></i>
                            <div class="text-sm text-yellow-700">
                                <strong>تحذير:</strong> سيتم حذف جميع النسخ الاحتياطية الأقدم من المدة المحددة نهائياً ولا يمكن استرجاعها.
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-2 space-x-reverse">
                        <button type="button" onclick="hideCleanModal()"
                                class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg transition-colors duration-200">
                            إلغاء
                        </button>
                        <button type="submit"
                                class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-trash ml-2"></i>
                            تنظيف النسخ
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        function showCleanModal() {
            document.getElementById('cleanModal').classList.remove('hidden');
        }

        function hideCleanModal() {
            document.getElementById('cleanModal').classList.add('hidden');
        }

        // إغلاق النافذة عند النقر خارجها
        document.getElementById('cleanModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideCleanModal();
            }
        });
    </script>

</body>
</html>
                    </div>
                </div>
            </div>
            
            <!-- الحجم الإجمالي -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-hdd text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">الحجم الإجمالي</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?php echo formatFileSize($backupStats['general']['total_size']); ?>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
        </div>
