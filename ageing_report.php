<?php
/**
 * تقرير أعمار الديون
 * Ageing Report Page
 */

define('APP_INIT', true);
require_once 'includes/init.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('reports_view');

// إنشاء مثيلات النماذج
$reportModel = new Report();
$customerModel = new Customer();

// معالجة الفلاتر
$filters = [
    'customer_id' => !empty($_GET['customer_id']) ? (int)$_GET['customer_id'] : null,
    'invoice_type' => sanitizeInput($_GET['invoice_type'] ?? ''),
    'age_category' => sanitizeInput($_GET['age_category'] ?? '')
];

// الحصول على البيانات
$ageingData = $reportModel->getAgeingReport($filters);
$ageingSummary = $reportModel->getAgeingSummary($filters);
$customers = $customerModel->getAllCustomers(true);

$pageTitle = 'تقرير أعمار الديون';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- خط عربي -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- أيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .card-hover:hover { transform: translateY(-2px); transition: all 0.3s ease; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        
        .age-current { border-right: 4px solid #10b981; }
        .age-1-30 { border-right: 4px solid #f59e0b; }
        .age-31-60 { border-right: 4px solid #ef4444; }
        .age-61-90 { border-right: 4px solid #8b5cf6; }
        .age-over-90 { border-right: 4px solid #dc2626; }
        
        @media print {
            .no-print { display: none !important; }
            .print-full-width { width: 100% !important; }
        }
    </style>
</head>
<body class="bg-gray-50">

    <!-- شريط التنقل العلوي -->
    <nav class="gradient-bg shadow-lg no-print">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="reports.php" class="flex-shrink-0 flex items-center text-white hover:text-gray-200">
                        <i class="fas fa-arrow-right text-xl ml-3"></i>
                        <span>العودة للتقارير</span>
                    </a>
                </div>
                
                <div class="flex items-center">
                    <h1 class="text-white text-xl font-bold">
                        <i class="fas fa-clock ml-2"></i>
                        <?php echo $pageTitle; ?>
                    </h1>
                </div>
                
                <div class="flex items-center space-x-4 space-x-reverse">
                    <!-- مكون الإشعارات -->
                    <?php include 'includes/notification_widget.php'; ?>
                    
                    <div class="text-white text-sm">
                        <i class="fas fa-user ml-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </div>
                    <a href="logout.php" class="text-white hover:text-gray-200 transition-colors duration-200">
                        <i class="fas fa-sign-out-alt ml-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        
        <!-- فلاتر التقرير -->
        <div class="bg-white shadow-lg rounded-lg mb-8 no-print">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">
                    <i class="fas fa-filter ml-2"></i>
                    فلاتر التقرير
                </h3>
            </div>
            
            <div class="p-6">
                <form method="GET" action="ageing_report.php" class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    
                    <!-- العميل -->
                    <div>
                        <label for="customer_id" class="block text-sm font-medium text-gray-700 mb-2">
                            العميل
                        </label>
                        <select id="customer_id" name="customer_id" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">جميع العملاء</option>
                            <?php foreach ($customers as $customer): ?>
                                <option value="<?php echo $customer['customer_id']; ?>" 
                                        <?php echo $filters['customer_id'] == $customer['customer_id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($customer['customer_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <!-- نوع الفاتورة -->
                    <div>
                        <label for="invoice_type" class="block text-sm font-medium text-gray-700 mb-2">
                            نوع الفاتورة
                        </label>
                        <select id="invoice_type" name="invoice_type" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">جميع الأنواع</option>
                            <option value="sales" <?php echo $filters['invoice_type'] === 'sales' ? 'selected' : ''; ?>>مبيعات</option>
                            <option value="purchase" <?php echo $filters['invoice_type'] === 'purchase' ? 'selected' : ''; ?>>مشتريات</option>
                        </select>
                    </div>
                    
                    <!-- فئة العمر -->
                    <div>
                        <label for="age_category" class="block text-sm font-medium text-gray-700 mb-2">
                            فئة العمر
                        </label>
                        <select id="age_category" name="age_category" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">جميع الفئات</option>
                            <option value="current" <?php echo $filters['age_category'] === 'current' ? 'selected' : ''; ?>>حالية (غير مستحقة)</option>
                            <option value="1-30" <?php echo $filters['age_category'] === '1-30' ? 'selected' : ''; ?>>1-30 يوم</option>
                            <option value="31-60" <?php echo $filters['age_category'] === '31-60' ? 'selected' : ''; ?>>31-60 يوم</option>
                            <option value="61-90" <?php echo $filters['age_category'] === '61-90' ? 'selected' : ''; ?>>61-90 يوم</option>
                            <option value="over_90" <?php echo $filters['age_category'] === 'over_90' ? 'selected' : ''; ?>>أكثر من 90 يوم</option>
                        </select>
                    </div>
                    
                    <!-- أزرار الإجراءات -->
                    <div class="md:col-span-3 flex space-x-4 space-x-reverse">
                        <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-search ml-2"></i>
                            عرض التقرير
                        </button>
                        <a href="ageing_report.php" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-times ml-2"></i>
                            مسح الفلاتر
                        </a>
                        <button type="button" onclick="window.print()" class="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-print ml-2"></i>
                            طباعة
                        </button>
                    </div>
                    
                </form>
            </div>
        </div>
        
        <!-- ملخص أعمار الديون -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            
            <!-- حالية -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover age-current">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-check text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">حالية</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?php echo formatMoney($ageingSummary['current_amount'] ?? 0); ?>
                                </dd>
                                <dd class="text-xs text-gray-500">
                                    (<?php echo $ageingSummary['current_count'] ?? 0; ?> فاتورة)
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 1-30 يوم -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover age-1-30">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-clock text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">1-30 يوم</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?php echo formatMoney($ageingSummary['days_1_30_amount'] ?? 0); ?>
                                </dd>
                                <dd class="text-xs text-gray-500">
                                    (<?php echo $ageingSummary['days_1_30_count'] ?? 0; ?> فاتورة)
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 31-60 يوم -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover age-31-60">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-exclamation text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">31-60 يوم</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?php echo formatMoney($ageingSummary['days_31_60_amount'] ?? 0); ?>
                                </dd>
                                <dd class="text-xs text-gray-500">
                                    (<?php echo $ageingSummary['days_31_60_count'] ?? 0; ?> فاتورة)
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 61-90 يوم -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover age-61-90">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-exclamation-triangle text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">61-90 يوم</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?php echo formatMoney($ageingSummary['days_61_90_amount'] ?? 0); ?>
                                </dd>
                                <dd class="text-xs text-gray-500">
                                    (<?php echo $ageingSummary['days_61_90_count'] ?? 0; ?> فاتورة)
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- أكثر من 90 يوم -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover age-over-90">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-red-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-ban text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">+90 يوم</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?php echo formatMoney($ageingSummary['over_90_amount'] ?? 0); ?>
                                </dd>
                                <dd class="text-xs text-gray-500">
                                    (<?php echo $ageingSummary['over_90_count'] ?? 0; ?> فاتورة)
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <!-- جدول تفاصيل أعمار الديون -->
        <div class="bg-white shadow-lg rounded-lg print-full-width">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-table ml-2"></i>
                        تفاصيل أعمار الديون (<?php echo count($ageingData); ?> فاتورة)
                    </h3>
                    <div class="text-sm text-gray-500">
                        إجمالي المبلغ: <?php echo formatMoney($ageingSummary['total_amount'] ?? 0); ?>
                    </div>
                </div>
            </div>

            <div class="overflow-x-auto">
                <?php if (empty($ageingData)): ?>
                    <div class="text-center py-12">
                        <i class="fas fa-file-invoice text-gray-400 text-6xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد فواتير مستحقة</h3>
                        <p class="text-gray-500">لا توجد فواتير تطابق معايير البحث المحددة</p>
                    </div>
                <?php else: ?>
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    رقم الفاتورة
                                </th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    العميل
                                </th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    تاريخ الفاتورة
                                </th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    تاريخ الاستحقاق
                                </th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    المبلغ الإجمالي
                                </th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    المبلغ المستحق
                                </th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    أيام التأخير
                                </th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    فئة العمر
                                </th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider no-print">
                                    إجراءات
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($ageingData as $invoice): ?>
                                <?php
                                $ageClass = 'age-' . str_replace('_', '-', $invoice['age_category']);
                                $statusColor = '';
                                $statusText = '';

                                switch ($invoice['age_category']) {
                                    case 'current':
                                        $statusColor = 'text-green-600 bg-green-100';
                                        $statusText = 'حالية';
                                        break;
                                    case '1-30':
                                        $statusColor = 'text-yellow-600 bg-yellow-100';
                                        $statusText = '1-30 يوم';
                                        break;
                                    case '31-60':
                                        $statusColor = 'text-red-600 bg-red-100';
                                        $statusText = '31-60 يوم';
                                        break;
                                    case '61-90':
                                        $statusColor = 'text-purple-600 bg-purple-100';
                                        $statusText = '61-90 يوم';
                                        break;
                                    case 'over_90':
                                        $statusColor = 'text-red-800 bg-red-200';
                                        $statusText = '+90 يوم';
                                        break;
                                }
                                ?>
                                <tr class="hover:bg-gray-50 <?php echo $ageClass; ?>">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        <?php echo htmlspecialchars($invoice['invoice_number']); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <div>
                                            <div class="font-medium"><?php echo htmlspecialchars($invoice['customer_name'] ?? 'غير محدد'); ?></div>
                                            <?php if (!empty($invoice['customer_code'])): ?>
                                                <div class="text-xs text-gray-500"><?php echo htmlspecialchars($invoice['customer_code']); ?></div>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo formatDate($invoice['invoice_date']); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo formatDate($invoice['due_date']); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo formatMoney($invoice['total_amount']); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        <?php echo formatMoney($invoice['outstanding_amount']); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php
                                        if ($invoice['days_overdue'] <= 0) {
                                            echo '<span class="text-green-600">غير مستحقة</span>';
                                        } else {
                                            echo '<span class="text-red-600">' . $invoice['days_overdue'] . ' يوم</span>';
                                        }
                                        ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $statusColor; ?>">
                                            <?php echo $statusText; ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium no-print">
                                        <a href="invoices.php?action=view&id=<?php echo $invoice['invoice_id']; ?>"
                                           class="text-blue-600 hover:text-blue-900 ml-3" title="عرض الفاتورة">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <?php if (!empty($invoice['phone'])): ?>
                                            <a href="tel:<?php echo $invoice['phone']; ?>"
                                               class="text-green-600 hover:text-green-900 ml-3" title="اتصال">
                                                <i class="fas fa-phone"></i>
                                            </a>
                                        <?php endif; ?>
                                        <?php if (!empty($invoice['email'])): ?>
                                            <a href="mailto:<?php echo $invoice['email']; ?>"
                                               class="text-purple-600 hover:text-purple-900" title="إرسال إيميل">
                                                <i class="fas fa-envelope"></i>
                                            </a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                        <tfoot class="bg-gray-50">
                            <tr>
                                <td colspan="4" class="px-6 py-4 text-sm font-medium text-gray-900">
                                    الإجمالي (<?php echo count($ageingData); ?> فاتورة)
                                </td>
                                <td class="px-6 py-4 text-sm font-medium text-gray-900">
                                    <?php echo formatMoney(array_sum(array_column($ageingData, 'total_amount'))); ?>
                                </td>
                                <td class="px-6 py-4 text-sm font-medium text-gray-900">
                                    <?php echo formatMoney(array_sum(array_column($ageingData, 'outstanding_amount'))); ?>
                                </td>
                                <td colspan="3" class="px-6 py-4"></td>
                            </tr>
                        </tfoot>
                    </table>
                <?php endif; ?>
            </div>
        </div>

        <!-- رسم بياني لأعمار الديون -->
        <?php if (!empty($ageingData)): ?>
            <div class="bg-white shadow-lg rounded-lg mt-8 no-print">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-chart-pie ml-2"></i>
                        توزيع أعمار الديون
                    </h3>
                </div>

                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <div>
                            <canvas id="ageingChart" width="400" height="400"></canvas>
                        </div>
                        <div>
                            <canvas id="ageingBarChart" width="400" height="400"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

    </div>

    <!-- JavaScript -->
    <script>
        <?php if (!empty($ageingData)): ?>
        // بيانات الرسم البياني
        const ageingData = {
            labels: ['حالية', '1-30 يوم', '31-60 يوم', '61-90 يوم', '+90 يوم'],
            datasets: [{
                data: [
                    <?php echo $ageingSummary['current_amount'] ?? 0; ?>,
                    <?php echo $ageingSummary['days_1_30_amount'] ?? 0; ?>,
                    <?php echo $ageingSummary['days_31_60_amount'] ?? 0; ?>,
                    <?php echo $ageingSummary['days_61_90_amount'] ?? 0; ?>,
                    <?php echo $ageingSummary['over_90_amount'] ?? 0; ?>
                ],
                backgroundColor: [
                    '#10b981',
                    '#f59e0b',
                    '#ef4444',
                    '#8b5cf6',
                    '#dc2626'
                ],
                borderWidth: 2,
                borderColor: '#ffffff'
            }]
        };

        // الرسم البياني الدائري
        const pieCtx = document.getElementById('ageingChart').getContext('2d');
        new Chart(pieCtx, {
            type: 'pie',
            data: ageingData,
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'توزيع المبالغ حسب أعمار الديون'
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // الرسم البياني العمودي
        const barCtx = document.getElementById('ageingBarChart').getContext('2d');
        new Chart(barCtx, {
            type: 'bar',
            data: {
                labels: ageingData.labels,
                datasets: [{
                    label: 'المبلغ',
                    data: ageingData.datasets[0].data,
                    backgroundColor: ageingData.datasets[0].backgroundColor,
                    borderColor: ageingData.datasets[0].backgroundColor,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'مقارنة المبالغ حسب أعمار الديون'
                    },
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value.toLocaleString() + ' ريال';
                            }
                        }
                    }
                }
            }
        });
        <?php endif; ?>
    </script>

</body>
</html>
