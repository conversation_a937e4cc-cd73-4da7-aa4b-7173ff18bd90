<?php
/**
 * صفحة تقرير المخزون
 * Inventory Report Page
 */

define('APP_INIT', true);
require_once 'includes/init.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('reports_view');

// إنشاء مثيلات النماذج
$reportModel = new Report();
$warehouseModel = new Warehouse();
$categoryModel = new Category();

// معالجة المرشحات
$warehouseFilter = !empty($_GET['warehouse']) ? (int)$_GET['warehouse'] : null;
$categoryFilter = !empty($_GET['category']) ? (int)$_GET['category'] : null;

// الحصول على تقرير المخزون
$inventoryReport = $reportModel->getInventoryReport($warehouseFilter, $categoryFilter);

// الحصول على قوائم المساعدة
$warehouses = $warehouseModel->getAllWarehouses();
$categories = $categoryModel->getAllCategories();

$pageTitle = 'تقرير المخزون';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- خط عربي -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- أيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        
        /* تحسين عرض التقرير */
        .report-table {
            font-size: 0.875rem;
        }
        
        .stock-status-نفد { background-color: #fee2e2; color: #991b1b; }
        .stock-status-منخفض { background-color: #fef3c7; color: #92400e; }
        .stock-status-طبيعي { background-color: #d1fae5; color: #065f46; }
        .stock-status-مرتفع { background-color: #dbeafe; color: #1e40af; }
        
        @media print {
            .no-print { display: none; }
            body { font-size: 12px; }
            .report-table { font-size: 11px; }
        }
    </style>
</head>
<body class="bg-gray-50">

    <!-- شريط التنقل العلوي -->
    <nav class="gradient-bg shadow-lg no-print">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="dashboard.php" class="flex-shrink-0 flex items-center text-white hover:text-gray-200">
                        <i class="fas fa-arrow-right text-xl ml-3"></i>
                        <span>العودة للوحة التحكم</span>
                    </a>
                </div>
                
                <div class="flex items-center">
                    <h1 class="text-white text-xl font-bold">
                        <i class="fas fa-boxes ml-2"></i>
                        <?php echo $pageTitle; ?>
                    </h1>
                </div>
                
                <div class="flex items-center space-x-4 space-x-reverse">
                    <span class="text-white text-sm">
                        <i class="fas fa-user ml-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </span>
                    <a href="logout.php" class="text-white hover:text-gray-200 transition-colors duration-200">
                        <i class="fas fa-sign-out-alt ml-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        
        <!-- عرض الرسائل -->
        <?php showAlert(); ?>
        
        <!-- بطاقات الإحصائيات -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            
            <!-- إجمالي الأصناف -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-box text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">إجمالي الأصناف</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?php echo number_format($inventoryReport['inventory_stats']['total_items']); ?>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- إجمالي المخازن -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-warehouse text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">المخازن</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?php echo number_format($inventoryReport['inventory_stats']['total_warehouses']); ?>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- قيمة المخزون -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-dollar-sign text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">قيمة المخزون</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?php echo formatMoney($inventoryReport['inventory_stats']['total_inventory_value']); ?>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- أصناف نفدت -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-exclamation-triangle text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">أصناف نفدت</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?php echo number_format($inventoryReport['inventory_stats']['out_of_stock_items']); ?>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- أصناف منخفضة -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-exclamation text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">أصناف منخفضة</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?php echo number_format($inventoryReport['inventory_stats']['low_stock_items']); ?>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
        </div>
        
        <!-- تقرير المخزون -->
        <div class="bg-white shadow-lg rounded-lg">
            
            <!-- رأس التقرير -->
            <div class="px-6 py-4 border-b border-gray-200 no-print">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-boxes ml-2"></i>
                        تقرير المخزون التفصيلي
                    </h3>
                    <div class="flex space-x-2 space-x-reverse">
                        <button onclick="window.print()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-print ml-2"></i>
                            طباعة
                        </button>
                        <a href="sales_report.php" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-chart-bar ml-2"></i>
                            تقرير المبيعات
                        </a>
                        <a href="reports.php" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-chart-line ml-2"></i>
                            جميع التقارير
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- فلترة -->
            <div class="px-6 py-4 bg-gray-50 border-b border-gray-200 no-print">
                <form method="GET" action="inventory_report.php" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">المخزن</label>
                        <select name="warehouse" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">جميع المخازن</option>
                            <?php foreach ($warehouses as $warehouse): ?>
                                <option value="<?php echo $warehouse['warehouse_id']; ?>" 
                                        <?php echo $warehouseFilter == $warehouse['warehouse_id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($warehouse['warehouse_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">الفئة</label>
                        <select name="category" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">جميع الفئات</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?php echo $category['category_id']; ?>" 
                                        <?php echo $categoryFilter == $category['category_id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($category['category_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="pt-6">
                        <button type="submit" class="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-search ml-2"></i>
                            عرض
                        </button>
                    </div>
                </form>
            </div>
            
            <!-- معلومات التقرير -->
            <div class="px-6 py-4 bg-blue-50 border-b border-gray-200">
                <div class="text-center">
                    <h2 class="text-xl font-bold text-gray-900 mb-2"><?php echo APP_NAME; ?></h2>
                    <h3 class="text-lg font-semibold text-gray-800 mb-1">تقرير المخزون التفصيلي</h3>
                    <p class="text-sm text-gray-600">
                        كما في <?php echo formatDate(date('Y-m-d')); ?>
                        <?php if ($warehouseFilter): ?>
                            - المخزن: <?php 
                            $selectedWarehouse = array_filter($warehouses, function($w) use ($warehouseFilter) {
                                return $w['warehouse_id'] == $warehouseFilter;
                            });
                            echo htmlspecialchars(reset($selectedWarehouse)['warehouse_name'] ?? '');
                            ?>
                        <?php endif; ?>
                        <?php if ($categoryFilter): ?>
                            - الفئة: <?php 
                            $selectedCategory = array_filter($categories, function($c) use ($categoryFilter) {
                                return $c['category_id'] == $categoryFilter;
                            });
                            echo htmlspecialchars(reset($selectedCategory)['category_name'] ?? '');
                            ?>
                        <?php endif; ?>
                    </p>
                    <p class="text-xs text-gray-500 mt-2">
                        تاريخ الطباعة: <?php echo formatDate(date('Y-m-d')); ?> - الوقت: <?php echo date('H:i'); ?>
                    </p>
                </div>
            </div>
            
            <!-- جدول المخزون -->
            <div class="overflow-x-auto">
                <?php if (empty($inventoryReport['inventory_data'])): ?>
                    <div class="text-center py-12">
                        <i class="fas fa-boxes text-gray-400 text-6xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد بيانات مخزون</h3>
                        <p class="text-gray-500">لا توجد أصناف مطابقة للمرشحات المحددة</p>
                    </div>
                <?php else: ?>
                    <table class="min-w-full divide-y divide-gray-200 report-table">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الصنف</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الفئة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المخزن</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الكمية المتاحة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الكمية المحجوزة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">متوسط التكلفة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">إجمالي القيمة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">حالة المخزون</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($inventoryReport['inventory_data'] as $item): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">
                                            <?php echo htmlspecialchars($item['item_name']); ?>
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            <?php echo htmlspecialchars($item['item_code']); ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo htmlspecialchars($item['category_name'] ?? 'غير محدد'); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            <?php echo htmlspecialchars($item['warehouse_name']); ?>
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            <?php echo htmlspecialchars($item['warehouse_code']); ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo formatNumber($item['available_quantity'], 3); ?>
                                        <?php if (!empty($item['unit_symbol'])): ?>
                                            <span class="text-gray-500"><?php echo htmlspecialchars($item['unit_symbol']); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo formatNumber($item['reserved_quantity'], 3); ?>
                                        <?php if (!empty($item['unit_symbol'])): ?>
                                            <span class="text-gray-500"><?php echo htmlspecialchars($item['unit_symbol']); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo formatMoney($item['average_cost']); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                                        <?php echo formatMoney($item['total_value']); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full stock-status-<?php echo $item['stock_status']; ?>">
                                            <?php echo htmlspecialchars($item['stock_status']); ?>
                                        </span>
                                        <?php if ($item['stock_status'] === 'منخفض' && $item['min_stock_level'] > 0): ?>
                                            <div class="text-xs text-gray-500 mt-1">
                                                الحد الأدنى: <?php echo formatNumber($item['min_stock_level'], 3); ?>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
            
        </div>
        
    </div>

</body>
</html>
