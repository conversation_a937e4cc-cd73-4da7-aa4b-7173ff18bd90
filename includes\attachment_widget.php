<?php
/**
 * مكون المرفقات
 * Attachment Widget Component
 */

// التأكد من أن المتغيرات المطلوبة موجودة
if (!isset($entityType) || !isset($entityId)) {
    return;
}

// إنشاء مثيل نموذج المرفقات
$attachmentModel = new Attachment();

// الحصول على مرفقات الكيان
$entityAttachments = $attachmentModel->getEntityAttachments($entityType, $entityId);

// معالجة رفع ملف جديد
if (isset($_POST['upload_attachment']) && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    if (verifyCSRFToken($csrfToken)) {
        $description = sanitizeInput($_POST['attachment_description'] ?? '');
        
        if (isset($_FILES['attachment_file'])) {
            $result = $attachmentModel->uploadFile($_FILES['attachment_file'], $entityType, $entityId, $description);
            
            if ($result['success']) {
                // إعادة تحميل الصفحة لإظهار المرفق الجديد
                header("Location: " . $_SERVER['REQUEST_URI']);
                exit;
            } else {
                $attachmentError = $result['message'];
            }
        }
    }
}
?>

<!-- مكون المرفقات -->
<div class="bg-white shadow rounded-lg">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">
            <i class="fas fa-paperclip ml-2"></i>
            المرفقات (<?php echo count($entityAttachments); ?>)
        </h3>
    </div>
    
    <div class="p-6">
        
        <!-- عرض رسالة خطأ إن وجدت -->
        <?php if (isset($attachmentError)): ?>
            <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                <i class="fas fa-exclamation-triangle ml-2"></i>
                <?php echo htmlspecialchars($attachmentError); ?>
            </div>
        <?php endif; ?>
        
        <!-- نموذج رفع ملف جديد -->
        <?php if (hasPermission('attachments_manage')): ?>
            <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                <h4 class="text-sm font-medium text-gray-700 mb-3">رفع ملف جديد</h4>
                
                <form method="POST" enctype="multipart/form-data" class="space-y-4">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="upload_attachment" value="1">
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="attachment_file_<?php echo $entityType . '_' . $entityId; ?>" class="block text-sm font-medium text-gray-700 mb-2">
                                اختيار الملف
                            </label>
                            <input type="file" 
                                   id="attachment_file_<?php echo $entityType . '_' . $entityId; ?>" 
                                   name="attachment_file" 
                                   required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        
                        <div>
                            <label for="attachment_description_<?php echo $entityType . '_' . $entityId; ?>" class="block text-sm font-medium text-gray-700 mb-2">
                                الوصف (اختياري)
                            </label>
                            <input type="text" 
                                   id="attachment_description_<?php echo $entityType . '_' . $entityId; ?>" 
                                   name="attachment_description"
                                   placeholder="وصف الملف"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                    
                    <div class="text-right">
                        <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-upload ml-2"></i>
                            رفع الملف
                        </button>
                    </div>
                </form>
            </div>
        <?php endif; ?>
        
        <!-- قائمة المرفقات -->
        <?php if (empty($entityAttachments)): ?>
            <div class="text-center py-8">
                <i class="fas fa-paperclip text-gray-400 text-4xl mb-4"></i>
                <p class="text-gray-500">لا توجد مرفقات</p>
            </div>
        <?php else: ?>
            <div class="space-y-3">
                <?php foreach ($entityAttachments as $attachment): ?>
                    <div class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                        
                        <!-- أيقونة الملف -->
                        <div class="flex-shrink-0 ml-3">
                            <i class="<?php echo $attachmentModel->getFileIcon($attachment['file_type'], $attachment['original_name']); ?> text-xl"></i>
                        </div>
                        
                        <!-- معلومات الملف -->
                        <div class="flex-1 min-w-0">
                            <h4 class="text-sm font-medium text-gray-900 truncate" title="<?php echo htmlspecialchars($attachment['original_name']); ?>">
                                <?php echo htmlspecialchars($attachment['original_name']); ?>
                            </h4>
                            
                            <?php if (!empty($attachment['description'])): ?>
                                <p class="text-xs text-gray-600 truncate" title="<?php echo htmlspecialchars($attachment['description']); ?>">
                                    <?php echo htmlspecialchars($attachment['description']); ?>
                                </p>
                            <?php endif; ?>
                            
                            <div class="flex items-center mt-1 text-xs text-gray-500">
                                <span><?php echo $attachmentModel->formatFileSize($attachment['file_size']); ?></span>
                                <span class="mx-2">•</span>
                                <span><?php echo formatDateTime($attachment['created_at']); ?></span>
                            </div>
                        </div>
                        
                        <!-- أزرار الإجراءات -->
                        <div class="flex items-center space-x-2 space-x-reverse">
                            
                            <!-- معاينة -->
                            <?php if (hasPermission('attachments_view')): ?>
                                <a href="view_attachment.php?id=<?php echo $attachment['attachment_id']; ?>" 
                                   target="_blank"
                                   class="text-green-600 hover:text-green-800 p-1" 
                                   title="معاينة">
                                    <i class="fas fa-eye"></i>
                                </a>
                            <?php endif; ?>
                            
                            <!-- تحميل -->
                            <?php if (hasPermission('attachments_view')): ?>
                                <a href="download_attachment.php?id=<?php echo $attachment['attachment_id']; ?>" 
                                   class="text-blue-600 hover:text-blue-800 p-1" 
                                   title="تحميل">
                                    <i class="fas fa-download"></i>
                                </a>
                            <?php endif; ?>
                            
                            <!-- حذف -->
                            <?php if (hasPermission('attachments_manage')): ?>
                                <form method="POST" action="attachments.php?action=delete&id=<?php echo $attachment['attachment_id']; ?>" 
                                      class="inline" 
                                      onsubmit="return confirm('هل أنت متأكد من حذف هذا المرفق؟')">
                                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                    <button type="submit" 
                                            class="text-red-600 hover:text-red-800 p-1" 
                                            title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            <?php endif; ?>
                            
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <!-- رابط عرض جميع المرفقات -->
            <?php if (count($entityAttachments) > 3): ?>
                <div class="text-center mt-4">
                    <a href="attachments.php?action=entity&entity_type=<?php echo $entityType; ?>&entity_id=<?php echo $entityId; ?>" 
                       class="text-blue-600 hover:text-blue-800 text-sm">
                        <i class="fas fa-list ml-1"></i>
                        عرض جميع المرفقات (<?php echo count($entityAttachments); ?>)
                    </a>
                </div>
            <?php endif; ?>
            
        <?php endif; ?>
        
    </div>
</div>

<!-- CSS إضافي للمكون -->
<style>
    .attachment-widget .file-icon {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 6px;
        background-color: #f3f4f6;
    }
    
    .attachment-widget .attachment-item:hover {
        background-color: #f9fafb;
        border-color: #d1d5db;
    }
    
    .attachment-widget .upload-area {
        border: 2px dashed #d1d5db;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
        transition: all 0.3s ease;
    }
    
    .attachment-widget .upload-area:hover {
        border-color: #3b82f6;
        background-color: #eff6ff;
    }
</style>
