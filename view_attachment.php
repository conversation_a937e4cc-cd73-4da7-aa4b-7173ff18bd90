<?php
/**
 * صفحة معاينة المرفقات
 * View Attachment Page
 */

define('APP_INIT', true);
require_once 'includes/init.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('attachments_view');

// إنشاء مثيل نموذج المرفقات
$attachmentModel = new Attachment();

// الحصول على معرف المرفق
$attachmentId = $_GET['id'] ?? null;

if (!$attachmentId) {
    header('Location: attachments.php?error=attachment_not_found');
    exit;
}

// الحصول على بيانات المرفق
$attachment = $attachmentModel->getAttachmentById($attachmentId);

if (!$attachment) {
    header('Location: attachments.php?error=attachment_not_found');
    exit;
}

// التحقق من وجود الملف
if (!file_exists($attachment['file_path'])) {
    header('Location: attachments.php?error=file_not_found');
    exit;
}

// تحديد نوع المعاينة
$canPreview = false;
$previewType = 'none';

if ($attachment['file_type'] === 'image') {
    $canPreview = true;
    $previewType = 'image';
} elseif ($attachment['mime_type'] === 'application/pdf') {
    $canPreview = true;
    $previewType = 'pdf';
} elseif (in_array($attachment['mime_type'], ['text/plain', 'text/html', 'text/css', 'text/javascript'])) {
    $canPreview = true;
    $previewType = 'text';
}

$pageTitle = 'معاينة المرفق - ' . $attachment['original_name'];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- خط عربي -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- أيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        
        .preview-container {
            max-height: 80vh;
            overflow: auto;
        }
        
        .preview-image {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .preview-text {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        
        .file-info-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
    </style>
</head>
<body class="bg-gray-50">

    <!-- شريط التنقل العلوي -->
    <nav class="gradient-bg shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="attachments.php" class="flex-shrink-0 flex items-center text-white hover:text-gray-200">
                        <i class="fas fa-arrow-right text-xl ml-3"></i>
                        <span>العودة للمرفقات</span>
                    </a>
                </div>
                
                <div class="flex items-center">
                    <h1 class="text-white text-xl font-bold">
                        <i class="fas fa-eye ml-2"></i>
                        معاينة المرفق
                    </h1>
                </div>
                
                <div class="flex items-center space-x-4 space-x-reverse">
                    <a href="download_attachment.php?id=<?php echo $attachmentId; ?>" 
                       class="text-white hover:text-gray-200 transition-colors duration-200">
                        <i class="fas fa-download ml-1"></i>
                        تحميل
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        
        <!-- معلومات الملف -->
        <div class="file-info-card">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                
                <div>
                    <h3 class="text-sm font-medium text-gray-500">اسم الملف</h3>
                    <p class="mt-1 text-lg font-semibold text-gray-900"><?php echo htmlspecialchars($attachment['original_name']); ?></p>
                </div>
                
                <div>
                    <h3 class="text-sm font-medium text-gray-500">حجم الملف</h3>
                    <p class="mt-1 text-lg font-semibold text-gray-900"><?php echo $attachmentModel->formatFileSize($attachment['file_size']); ?></p>
                </div>
                
                <div>
                    <h3 class="text-sm font-medium text-gray-500">نوع الملف</h3>
                    <p class="mt-1 text-lg font-semibold text-gray-900"><?php echo htmlspecialchars($attachment['mime_type']); ?></p>
                </div>
                
                <div>
                    <h3 class="text-sm font-medium text-gray-500">تاريخ الرفع</h3>
                    <p class="mt-1 text-lg font-semibold text-gray-900"><?php echo formatDateTime($attachment['created_at']); ?></p>
                </div>
                
            </div>
            
            <?php if (!empty($attachment['description'])): ?>
                <div class="mt-6">
                    <h3 class="text-sm font-medium text-gray-500">الوصف</h3>
                    <p class="mt-1 text-gray-900"><?php echo htmlspecialchars($attachment['description']); ?></p>
                </div>
            <?php endif; ?>
            
            <div class="mt-6">
                <h3 class="text-sm font-medium text-gray-500">معلومات إضافية</h3>
                <div class="mt-1 flex items-center text-sm text-gray-600">
                    <span>مرتبط بـ: <?php echo ucfirst($attachment['entity_type']); ?> #<?php echo $attachment['entity_id']; ?></span>
                    <span class="mx-2">•</span>
                    <span>رفع بواسطة: <?php echo htmlspecialchars($attachment['uploaded_by_name'] ?? 'غير محدد'); ?></span>
                </div>
            </div>
        </div>
        
        <!-- معاينة الملف -->
        <div class="bg-white shadow-lg rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">
                    <i class="fas fa-search ml-2"></i>
                    معاينة الملف
                </h3>
            </div>
            
            <div class="p-6">
                <?php if (!$canPreview): ?>
                    <!-- لا يمكن معاينة الملف -->
                    <div class="text-center py-12">
                        <i class="<?php echo $attachmentModel->getFileIcon($attachment['file_type'], $attachment['original_name']); ?> text-6xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">لا يمكن معاينة هذا النوع من الملفات</h3>
                        <p class="text-gray-500 mb-6">يمكنك تحميل الملف لعرضه في التطبيق المناسب</p>
                        <a href="download_attachment.php?id=<?php echo $attachmentId; ?>" 
                           class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors duration-200">
                            <i class="fas fa-download ml-2"></i>
                            تحميل الملف
                        </a>
                    </div>
                    
                <?php elseif ($previewType === 'image'): ?>
                    <!-- معاينة الصورة -->
                    <div class="preview-container text-center">
                        <img src="<?php echo htmlspecialchars($attachment['file_path']); ?>" 
                             alt="<?php echo htmlspecialchars($attachment['original_name']); ?>"
                             class="preview-image mx-auto">
                    </div>
                    
                <?php elseif ($previewType === 'pdf'): ?>
                    <!-- معاينة PDF -->
                    <div class="preview-container">
                        <iframe src="<?php echo htmlspecialchars($attachment['file_path']); ?>" 
                                width="100%" height="600px" 
                                style="border: none; border-radius: 8px;">
                            <p>متصفحك لا يدعم معاينة ملفات PDF. 
                               <a href="download_attachment.php?id=<?php echo $attachmentId; ?>">انقر هنا لتحميل الملف</a>
                            </p>
                        </iframe>
                    </div>
                    
                <?php elseif ($previewType === 'text'): ?>
                    <!-- معاينة النص -->
                    <div class="preview-container">
                        <div class="preview-text">
                            <?php
                            $fileContent = file_get_contents($attachment['file_path']);
                            // تحديد حجم المعاينة (أول 10KB)
                            if (strlen($fileContent) > 10240) {
                                $fileContent = substr($fileContent, 0, 10240) . "\n\n... (تم اقتطاع الملف - حمل الملف كاملاً لعرض المحتوى كاملاً)";
                            }
                            echo htmlspecialchars($fileContent);
                            ?>
                        </div>
                    </div>
                    
                <?php endif; ?>
            </div>
        </div>
        
        <!-- أزرار الإجراءات -->
        <div class="mt-6 text-center">
            <div class="inline-flex space-x-4 space-x-reverse">
                <a href="download_attachment.php?id=<?php echo $attachmentId; ?>" 
                   class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors duration-200">
                    <i class="fas fa-download ml-2"></i>
                    تحميل الملف
                </a>
                
                <a href="attachments.php" 
                   class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-3 rounded-lg transition-colors duration-200">
                    <i class="fas fa-list ml-2"></i>
                    العودة للقائمة
                </a>
            </div>
        </div>
        
    </div>

</body>
</html>
