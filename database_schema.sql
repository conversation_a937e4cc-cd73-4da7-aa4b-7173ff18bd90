-- =====================================================
-- نظام الحسابات والمخازن - قاعدة البيانات الكاملة
-- إعداد: نظام محاسبي متكامل مع دعم الأصناف متعددة الوحدات
-- الترميز: UTF-8 لدعم اللغة العربية
-- التاريخ: 2025-06-30
-- =====================================================

-- إنشاء قاعدة البيانات مع دعم UTF-8
DROP DATABASE IF EXISTS accounting_inventory_system;
CREATE DATABASE accounting_inventory_system 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE accounting_inventory_system;

-- =====================================================
-- 1. جدول المستخدمين (users)
-- وصف: يحتوي على بيانات المستخدمين وصلاحياتهم
-- =====================================================
CREATE TABLE users (
    user_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'معرف المستخدم الفريد',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT 'اسم المستخدم للدخول',
    password_hash VARCHAR(255) NOT NULL COMMENT 'كلمة المرور المشفرة',
    full_name VARCHAR(100) NOT NULL COMMENT 'الاسم الكامل للمستخدم',
    email VARCHAR(100) UNIQUE COMMENT 'البريد الإلكتروني',
    phone VARCHAR(20) COMMENT 'رقم الهاتف',
    role ENUM('admin', 'accountant', 'warehouse_manager', 'sales', 'user') 
         DEFAULT 'user' COMMENT 'دور المستخدم في النظام',
    permissions JSON COMMENT 'صلاحيات المستخدم المفصلة',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'حالة نشاط المستخدم',
    last_login DATETIME COMMENT 'آخر تسجيل دخول',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ إنشاء الحساب',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث',
    created_by INT COMMENT 'المستخدم الذي أنشأ هذا الحساب',
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB COMMENT='جدول المستخدمين وصلاحياتهم';

-- =====================================================
-- 2. جدول الفروع (branches)
-- وصف: يحتوي على بيانات فروع الشركة
-- =====================================================
CREATE TABLE branches (
    branch_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'معرف الفرع الفريد',
    branch_code VARCHAR(10) NOT NULL UNIQUE COMMENT 'كود الفرع',
    branch_name VARCHAR(100) NOT NULL COMMENT 'اسم الفرع',
    address TEXT COMMENT 'عنوان الفرع',
    city VARCHAR(50) COMMENT 'المدينة',
    phone VARCHAR(20) COMMENT 'رقم الهاتف',
    fax VARCHAR(20) COMMENT 'رقم الفاكس',
    email VARCHAR(100) COMMENT 'البريد الإلكتروني',
    manager_name VARCHAR(100) COMMENT 'اسم مدير الفرع',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'حالة نشاط الفرع',
    notes TEXT COMMENT 'ملاحظات إضافية',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث',
    created_by INT COMMENT 'المستخدم الذي أنشأ السجل',
    
    INDEX idx_branch_code (branch_code),
    INDEX idx_branch_name (branch_name),
    INDEX idx_is_active (is_active),
    
    FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE SET NULL
) ENGINE=InnoDB COMMENT='جدول فروع الشركة';

-- =====================================================
-- 3. جدول شجرة الحسابات (chart_of_accounts)
-- وصف: يحتوي على الهيكل المحاسبي للشركة
-- =====================================================
CREATE TABLE chart_of_accounts (
    account_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'معرف الحساب الفريد',
    account_code VARCHAR(20) NOT NULL UNIQUE COMMENT 'رقم الحساب',
    account_name VARCHAR(150) NOT NULL COMMENT 'اسم الحساب',
    account_type ENUM('assets', 'liabilities', 'equity', 'revenue', 'expenses') 
                 NOT NULL COMMENT 'نوع الحساب (أصول/خصوم/حقوق ملكية/إيرادات/مصروفات)',
    parent_account_id INT COMMENT 'معرف الحساب الأب',
    account_level INT NOT NULL DEFAULT 1 COMMENT 'مستوى الحساب في الشجرة',
    is_main_account BOOLEAN DEFAULT FALSE COMMENT 'هل هو حساب رئيسي',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'حالة نشاط الحساب',
    description TEXT COMMENT 'وصف الحساب',
    branch_id INT COMMENT 'معرف الفرع المرتبط بالحساب',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث',
    created_by INT COMMENT 'المستخدم الذي أنشأ الحساب',
    
    INDEX idx_account_code (account_code),
    INDEX idx_account_name (account_name),
    INDEX idx_account_type (account_type),
    INDEX idx_parent_account (parent_account_id),
    INDEX idx_account_level (account_level),
    INDEX idx_is_active (is_active),
    
    FOREIGN KEY (parent_account_id) REFERENCES chart_of_accounts(account_id) ON DELETE CASCADE,
    FOREIGN KEY (branch_id) REFERENCES branches(branch_id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE SET NULL
) ENGINE=InnoDB COMMENT='جدول شجرة الحسابات المحاسبية';

-- =====================================================
-- 4. جدول حركة الحسابات (account_transactions)
-- وصف: يحتوي على جميع القيود المحاسبية
-- =====================================================
CREATE TABLE account_transactions (
    transaction_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'معرف المعاملة الفريد',
    voucher_number VARCHAR(20) NOT NULL COMMENT 'رقم القيد',
    transaction_date DATE NOT NULL COMMENT 'تاريخ القيد',
    account_id INT NOT NULL COMMENT 'معرف الحساب',
    debit_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT 'المبلغ المدين',
    credit_amount DECIMAL(15,2) DEFAULT 0.00 COMMENT 'المبلغ الدائن',
    description TEXT COMMENT 'بيان القيد',
    reference_number VARCHAR(50) COMMENT 'رقم المرجع',
    reference_type ENUM('manual', 'sales', 'purchase', 'inventory', 'payment', 'receipt') 
                   DEFAULT 'manual' COMMENT 'نوع العملية المرجعية',
    reference_id INT COMMENT 'معرف العملية المرجعية',
    branch_id INT COMMENT 'معرف الفرع',
    is_posted BOOLEAN DEFAULT FALSE COMMENT 'هل تم ترحيل القيد',
    posted_at DATETIME COMMENT 'تاريخ الترحيل',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    created_by INT NOT NULL COMMENT 'المستخدم المسؤول عن القيد',
    
    INDEX idx_voucher_number (voucher_number),
    INDEX idx_transaction_date (transaction_date),
    INDEX idx_account_id (account_id),
    INDEX idx_reference_type (reference_type),
    INDEX idx_reference_id (reference_id),
    INDEX idx_branch_id (branch_id),
    INDEX idx_is_posted (is_posted),
    INDEX idx_created_by (created_by),
    
    FOREIGN KEY (account_id) REFERENCES chart_of_accounts(account_id) ON DELETE CASCADE,
    FOREIGN KEY (branch_id) REFERENCES branches(branch_id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE RESTRICT
) ENGINE=InnoDB COMMENT='جدول حركة الحسابات والقيود المحاسبية';

-- =====================================================
-- 5. جدول فئات الأصناف (item_categories)
-- وصف: يحتوي على تصنيفات الأصناف
-- =====================================================
CREATE TABLE item_categories (
    category_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'معرف الفئة الفريد',
    category_code VARCHAR(10) NOT NULL UNIQUE COMMENT 'كود الفئة',
    category_name VARCHAR(100) NOT NULL COMMENT 'اسم الفئة',
    parent_category_id INT COMMENT 'معرف الفئة الأب',
    description TEXT COMMENT 'وصف الفئة',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'حالة نشاط الفئة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث',
    created_by INT COMMENT 'المستخدم الذي أنشأ الفئة',
    
    INDEX idx_category_code (category_code),
    INDEX idx_category_name (category_name),
    INDEX idx_parent_category (parent_category_id),
    INDEX idx_is_active (is_active),
    
    FOREIGN KEY (parent_category_id) REFERENCES item_categories(category_id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE SET NULL
) ENGINE=InnoDB COMMENT='جدول فئات وتصنيفات الأصناف';

-- =====================================================
-- 6. جدول الوحدات (units)
-- وصف: يحتوي على وحدات القياس المختلفة
-- =====================================================
CREATE TABLE units (
    unit_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'معرف الوحدة الفريد',
    unit_code VARCHAR(10) NOT NULL UNIQUE COMMENT 'كود الوحدة',
    unit_name VARCHAR(50) NOT NULL COMMENT 'اسم الوحدة',
    unit_symbol VARCHAR(10) COMMENT 'رمز الوحدة',
    unit_type ENUM('weight', 'length', 'volume', 'piece', 'area', 'time', 'other')
              DEFAULT 'piece' COMMENT 'نوع الوحدة',
    is_base_unit BOOLEAN DEFAULT FALSE COMMENT 'هل هي وحدة أساسية',
    description TEXT COMMENT 'وصف الوحدة',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'حالة نشاط الوحدة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث',
    created_by INT COMMENT 'المستخدم الذي أنشأ الوحدة',

    INDEX idx_unit_code (unit_code),
    INDEX idx_unit_name (unit_name),
    INDEX idx_unit_type (unit_type),
    INDEX idx_is_active (is_active),

    FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE SET NULL
) ENGINE=InnoDB COMMENT='جدول وحدات القياس';

-- =====================================================
-- 7. جدول الأصناف (items)
-- وصف: يحتوي على بيانات الأصناف الأساسية
-- =====================================================
CREATE TABLE items (
    item_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'معرف الصنف الفريد',
    item_code VARCHAR(20) NOT NULL UNIQUE COMMENT 'كود الصنف',
    item_name VARCHAR(150) NOT NULL COMMENT 'اسم الصنف',
    item_description TEXT COMMENT 'وصف الصنف',
    category_id INT COMMENT 'معرف فئة الصنف',
    base_unit_id INT NOT NULL COMMENT 'معرف الوحدة الأساسية',
    barcode VARCHAR(50) COMMENT 'الباركود',
    item_type ENUM('product', 'service', 'raw_material', 'finished_goods')
              DEFAULT 'product' COMMENT 'نوع الصنف',
    cost_method ENUM('fifo', 'lifo', 'average', 'specific')
                DEFAULT 'average' COMMENT 'طريقة تقييم التكلفة',
    min_stock_level DECIMAL(10,3) DEFAULT 0 COMMENT 'الحد الأدنى للمخزون',
    max_stock_level DECIMAL(10,3) DEFAULT 0 COMMENT 'الحد الأقصى للمخزون',
    reorder_level DECIMAL(10,3) DEFAULT 0 COMMENT 'نقطة إعادة الطلب',
    shelf_life_days INT COMMENT 'مدة الصلاحية بالأيام',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'حالة نشاط الصنف',
    has_serial_numbers BOOLEAN DEFAULT FALSE COMMENT 'هل يحتاج أرقام تسلسلية',
    has_expiry_date BOOLEAN DEFAULT FALSE COMMENT 'هل له تاريخ انتهاء صلاحية',
    notes TEXT COMMENT 'ملاحظات إضافية',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث',
    created_by INT COMMENT 'المستخدم الذي أنشأ الصنف',

    INDEX idx_item_code (item_code),
    INDEX idx_item_name (item_name),
    INDEX idx_category_id (category_id),
    INDEX idx_base_unit_id (base_unit_id),
    INDEX idx_barcode (barcode),
    INDEX idx_item_type (item_type),
    INDEX idx_is_active (is_active),

    FOREIGN KEY (category_id) REFERENCES item_categories(category_id) ON DELETE SET NULL,
    FOREIGN KEY (base_unit_id) REFERENCES units(unit_id) ON DELETE RESTRICT,
    FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE SET NULL
) ENGINE=InnoDB COMMENT='جدول الأصناف الأساسية';

-- =====================================================
-- 8. جدول وحدات الأصناف (item_units)
-- وصف: يحتوي على الوحدات البديلة للأصناف مع معاملات التحويل
-- =====================================================
CREATE TABLE item_units (
    item_unit_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'معرف وحدة الصنف الفريد',
    item_id INT NOT NULL COMMENT 'معرف الصنف',
    unit_id INT NOT NULL COMMENT 'معرف الوحدة',
    conversion_factor DECIMAL(10,6) NOT NULL DEFAULT 1.000000 COMMENT 'معامل التحويل من الوحدة الأساسية',
    purchase_price DECIMAL(12,2) DEFAULT 0.00 COMMENT 'سعر الشراء بهذه الوحدة',
    sale_price DECIMAL(12,2) DEFAULT 0.00 COMMENT 'سعر البيع بهذه الوحدة',
    wholesale_price DECIMAL(12,2) DEFAULT 0.00 COMMENT 'سعر الجملة بهذه الوحدة',
    is_default BOOLEAN DEFAULT FALSE COMMENT 'هل هي الوحدة الافتراضية للعرض',
    is_purchasable BOOLEAN DEFAULT TRUE COMMENT 'هل يمكن الشراء بهذه الوحدة',
    is_saleable BOOLEAN DEFAULT TRUE COMMENT 'هل يمكن البيع بهذه الوحدة',
    barcode VARCHAR(50) COMMENT 'باركود خاص بهذه الوحدة',
    notes TEXT COMMENT 'ملاحظات خاصة بهذه الوحدة',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث',
    created_by INT COMMENT 'المستخدم الذي أنشأ السجل',

    UNIQUE KEY unique_item_unit (item_id, unit_id),
    INDEX idx_item_id (item_id),
    INDEX idx_unit_id (unit_id),
    INDEX idx_conversion_factor (conversion_factor),
    INDEX idx_barcode (barcode),
    INDEX idx_is_default (is_default),

    FOREIGN KEY (item_id) REFERENCES items(item_id) ON DELETE CASCADE,
    FOREIGN KEY (unit_id) REFERENCES units(unit_id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE SET NULL
) ENGINE=InnoDB COMMENT='جدول وحدات الأصناف ومعاملات التحويل';

-- =====================================================
-- 9. جدول المخازن (warehouses)
-- وصف: يحتوي على بيانات المخازن
-- =====================================================
CREATE TABLE warehouses (
    warehouse_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'معرف المخزن الفريد',
    warehouse_code VARCHAR(10) NOT NULL UNIQUE COMMENT 'كود المخزن',
    warehouse_name VARCHAR(100) NOT NULL COMMENT 'اسم المخزن',
    warehouse_location TEXT COMMENT 'موقع المخزن',
    warehouse_type ENUM('main', 'branch', 'temporary', 'virtual')
                   DEFAULT 'main' COMMENT 'نوع المخزن',
    manager_name VARCHAR(100) COMMENT 'اسم مسؤول المخزن',
    phone VARCHAR(20) COMMENT 'رقم هاتف المخزن',
    email VARCHAR(100) COMMENT 'البريد الإلكتروني',
    branch_id INT COMMENT 'معرف الفرع التابع له المخزن',
    capacity DECIMAL(10,2) COMMENT 'السعة التخزينية',
    capacity_unit VARCHAR(20) COMMENT 'وحدة قياس السعة',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'حالة نشاط المخزن',
    allow_negative_stock BOOLEAN DEFAULT FALSE COMMENT 'السماح بالمخزون السالب',
    notes TEXT COMMENT 'ملاحظات إضافية',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث',
    created_by INT COMMENT 'المستخدم الذي أنشأ المخزن',

    INDEX idx_warehouse_code (warehouse_code),
    INDEX idx_warehouse_name (warehouse_name),
    INDEX idx_warehouse_type (warehouse_type),
    INDEX idx_branch_id (branch_id),
    INDEX idx_is_active (is_active),

    FOREIGN KEY (branch_id) REFERENCES branches(branch_id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE SET NULL
) ENGINE=InnoDB COMMENT='جدول المخازن';

-- =====================================================
-- 10. جدول أرصدة المخزون (inventory_balances)
-- وصف: يحتوي على أرصدة الأصناف في المخازن
-- =====================================================
CREATE TABLE inventory_balances (
    balance_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'معرف الرصيد الفريد',
    item_id INT NOT NULL COMMENT 'معرف الصنف',
    warehouse_id INT NOT NULL COMMENT 'معرف المخزن',
    unit_id INT NOT NULL COMMENT 'معرف الوحدة',
    quantity DECIMAL(15,6) DEFAULT 0.000000 COMMENT 'الكمية المتاحة',
    reserved_quantity DECIMAL(15,6) DEFAULT 0.000000 COMMENT 'الكمية المحجوزة',
    available_quantity DECIMAL(15,6) GENERATED ALWAYS AS (quantity - reserved_quantity) STORED COMMENT 'الكمية المتاحة للبيع',
    average_cost DECIMAL(12,6) DEFAULT 0.000000 COMMENT 'متوسط التكلفة',
    last_cost DECIMAL(12,6) DEFAULT 0.000000 COMMENT 'آخر تكلفة',
    total_value DECIMAL(15,2) GENERATED ALWAYS AS (quantity * average_cost) STORED COMMENT 'إجمالي قيمة المخزون',
    last_movement_date DATETIME COMMENT 'تاريخ آخر حركة',
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث',

    UNIQUE KEY unique_item_warehouse_unit (item_id, warehouse_id, unit_id),
    INDEX idx_item_id (item_id),
    INDEX idx_warehouse_id (warehouse_id),
    INDEX idx_unit_id (unit_id),
    INDEX idx_quantity (quantity),
    INDEX idx_last_movement_date (last_movement_date),

    FOREIGN KEY (item_id) REFERENCES items(item_id) ON DELETE CASCADE,
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(warehouse_id) ON DELETE CASCADE,
    FOREIGN KEY (unit_id) REFERENCES units(unit_id) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='جدول أرصدة المخزون';

-- =====================================================
-- 11. جدول حركات المخزون (inventory_movements)
-- وصف: يحتوي على جميع حركات المخزون
-- =====================================================
CREATE TABLE inventory_movements (
    movement_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'معرف الحركة الفريد',
    movement_number VARCHAR(20) NOT NULL COMMENT 'رقم الحركة',
    movement_date DATE NOT NULL COMMENT 'تاريخ الحركة',
    movement_type ENUM('in', 'out', 'transfer', 'adjustment', 'opening_balance')
                  NOT NULL COMMENT 'نوع الحركة',
    item_id INT NOT NULL COMMENT 'معرف الصنف',
    warehouse_id INT NOT NULL COMMENT 'معرف المخزن',
    unit_id INT NOT NULL COMMENT 'معرف الوحدة',
    quantity DECIMAL(15,6) NOT NULL COMMENT 'الكمية',
    unit_cost DECIMAL(12,6) DEFAULT 0.000000 COMMENT 'تكلفة الوحدة',
    total_cost DECIMAL(15,2) GENERATED ALWAYS AS (quantity * unit_cost) STORED COMMENT 'إجمالي التكلفة',
    reference_type ENUM('purchase', 'sale', 'transfer', 'adjustment', 'opening', 'return')
                   COMMENT 'نوع العملية المرجعية',
    reference_id INT COMMENT 'معرف العملية المرجعية',
    reference_number VARCHAR(50) COMMENT 'رقم المرجع',
    from_warehouse_id INT COMMENT 'المخزن المحول منه (في حالة التحويل)',
    to_warehouse_id INT COMMENT 'المخزن المحول إليه (في حالة التحويل)',
    notes TEXT COMMENT 'ملاحظات الحركة',
    batch_number VARCHAR(50) COMMENT 'رقم الدفعة',
    expiry_date DATE COMMENT 'تاريخ انتهاء الصلاحية',
    serial_numbers JSON COMMENT 'الأرقام التسلسلية',
    is_posted BOOLEAN DEFAULT FALSE COMMENT 'هل تم ترحيل الحركة',
    posted_at DATETIME COMMENT 'تاريخ الترحيل',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    created_by INT NOT NULL COMMENT 'المستخدم المسؤول عن الحركة',

    INDEX idx_movement_number (movement_number),
    INDEX idx_movement_date (movement_date),
    INDEX idx_movement_type (movement_type),
    INDEX idx_item_id (item_id),
    INDEX idx_warehouse_id (warehouse_id),
    INDEX idx_unit_id (unit_id),
    INDEX idx_reference_type (reference_type),
    INDEX idx_reference_id (reference_id),
    INDEX idx_batch_number (batch_number),
    INDEX idx_expiry_date (expiry_date),
    INDEX idx_is_posted (is_posted),
    INDEX idx_created_by (created_by),

    FOREIGN KEY (item_id) REFERENCES items(item_id) ON DELETE CASCADE,
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(warehouse_id) ON DELETE CASCADE,
    FOREIGN KEY (unit_id) REFERENCES units(unit_id) ON DELETE CASCADE,
    FOREIGN KEY (from_warehouse_id) REFERENCES warehouses(warehouse_id) ON DELETE SET NULL,
    FOREIGN KEY (to_warehouse_id) REFERENCES warehouses(warehouse_id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE RESTRICT
) ENGINE=InnoDB COMMENT='جدول حركات المخزون';

-- =====================================================
-- 12. جدول سجل الأنشطة (activity_logs)
-- وصف: يحتوي على سجل أنشطة المستخدمين
-- =====================================================
CREATE TABLE activity_logs (
    log_id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'معرف السجل الفريد',
    user_id INT COMMENT 'معرف المستخدم',
    action VARCHAR(100) NOT NULL COMMENT 'نوع النشاط',
    details TEXT COMMENT 'تفاصيل النشاط',
    ip_address VARCHAR(45) COMMENT 'عنوان IP',
    user_agent TEXT COMMENT 'معلومات المتصفح',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ النشاط',

    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at),
    INDEX idx_ip_address (ip_address),

    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL
) ENGINE=InnoDB COMMENT='جدول سجل أنشطة المستخدمين';

-- =====================================================
-- إدراج البيانات التجريبية الأساسية
-- =====================================================

-- إدراج المستخدم الإداري الأساسي
INSERT INTO users (username, password_hash, full_name, email, phone, role, permissions, is_active, created_by) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', '<EMAIL>', '**********', 'admin',
 '{"users": {"create": true, "read": true, "update": true, "delete": true}, "accounts": {"create": true, "read": true, "update": true, "delete": true}, "inventory": {"create": true, "read": true, "update": true, "delete": true}, "reports": {"create": true, "read": true, "update": true, "delete": true}}',
 TRUE, NULL);

-- إدراج الفروع الأساسية
INSERT INTO branches (branch_code, branch_name, address, city, phone, email, manager_name, is_active, created_by) VALUES
('BR001', 'الفرع الرئيسي', 'شارع الملك فهد، الرياض', 'الرياض', '011-1234567', '<EMAIL>', 'أحمد محمد', TRUE, 1),
('BR002', 'فرع جدة', 'شارع التحلية، جدة', 'جدة', '012-7654321', '<EMAIL>', 'محمد علي', TRUE, 1);

-- إدراج شجرة الحسابات الأساسية
INSERT INTO chart_of_accounts (account_code, account_name, account_type, parent_account_id, account_level, is_main_account, is_active, description, created_by) VALUES
-- الحسابات الرئيسية
('1', 'الأصول', 'assets', NULL, 1, TRUE, TRUE, 'مجموعة الأصول الرئيسية', 1),
('2', 'الخصوم', 'liabilities', NULL, 1, TRUE, TRUE, 'مجموعة الخصوم الرئيسية', 1),
('3', 'حقوق الملكية', 'equity', NULL, 1, TRUE, TRUE, 'مجموعة حقوق الملكية', 1),
('4', 'الإيرادات', 'revenue', NULL, 1, TRUE, TRUE, 'مجموعة الإيرادات', 1),
('5', 'المصروفات', 'expenses', NULL, 1, TRUE, TRUE, 'مجموعة المصروفات', 1),

-- الأصول الفرعية
('11', 'الأصول المتداولة', 'assets', 1, 2, FALSE, TRUE, 'الأصول قصيرة الأجل', 1),
('12', 'الأصول الثابتة', 'assets', 1, 2, FALSE, TRUE, 'الأصول طويلة الأجل', 1),
('111', 'النقدية', 'assets', 2, 3, FALSE, TRUE, 'النقدية والبنوك', 1),
('112', 'العملاء', 'assets', 2, 3, FALSE, TRUE, 'حسابات العملاء', 1),
('113', 'المخزون', 'assets', 2, 3, FALSE, TRUE, 'مخزون البضائع', 1),

-- الخصوم الفرعية
('21', 'الخصوم المتداولة', 'liabilities', 3, 2, FALSE, TRUE, 'الخصوم قصيرة الأجل', 1),
('22', 'الخصوم طويلة الأجل', 'liabilities', 3, 2, FALSE, TRUE, 'الخصوم طويلة الأجل', 1),
('211', 'الموردون', 'liabilities', 8, 3, FALSE, TRUE, 'حسابات الموردين', 1),
('212', 'مصروفات مستحقة', 'liabilities', 8, 3, FALSE, TRUE, 'المصروفات المستحقة الدفع', 1),

-- الإيرادات الفرعية
('41', 'إيرادات المبيعات', 'revenue', 5, 2, FALSE, TRUE, 'إيرادات بيع البضائع', 1),
('42', 'إيرادات أخرى', 'revenue', 5, 2, FALSE, TRUE, 'الإيرادات الأخرى', 1),

-- المصروفات الفرعية
('51', 'تكلفة البضاعة المباعة', 'expenses', 6, 2, FALSE, TRUE, 'تكلفة البضائع المباعة', 1),
('52', 'مصروفات إدارية', 'expenses', 6, 2, FALSE, TRUE, 'المصروفات الإدارية', 1),
('53', 'مصروفات بيعية', 'expenses', 6, 2, FALSE, TRUE, 'مصروفات البيع والتسويق', 1);

-- إدراج فئات الأصناف
INSERT INTO item_categories (category_code, category_name, parent_category_id, description, is_active, created_by) VALUES
('CAT001', 'المواد الغذائية', NULL, 'فئة المواد الغذائية الأساسية', TRUE, 1),
('CAT002', 'المشروبات', NULL, 'فئة المشروبات بأنواعها', TRUE, 1),
('CAT003', 'منتجات الألبان', 1, 'منتجات الحليب والألبان', TRUE, 1),
('CAT004', 'الخضروات والفواكه', 1, 'الخضروات والفواكه الطازجة', TRUE, 1),
('CAT005', 'اللحوم والدواجن', 1, 'اللحوم الحمراء والبيضاء', TRUE, 1);

-- إدراج الوحدات الأساسية
INSERT INTO units (unit_code, unit_name, unit_symbol, unit_type, is_base_unit, description, is_active, created_by) VALUES
('KG', 'كيلوجرام', 'كجم', 'weight', TRUE, 'وحدة الوزن الأساسية', TRUE, 1),
('G', 'جرام', 'جم', 'weight', FALSE, 'وحدة الوزن الفرعية', TRUE, 1),
('L', 'لتر', 'لتر', 'volume', TRUE, 'وحدة الحجم الأساسية', TRUE, 1),
('ML', 'مليلتر', 'مل', 'volume', FALSE, 'وحدة الحجم الفرعية', TRUE, 1),
('PCS', 'قطعة', 'قطعة', 'piece', TRUE, 'وحدة العدد', TRUE, 1),
('BOX', 'صندوق', 'صندوق', 'piece', FALSE, 'وحدة التعبئة', TRUE, 1),
('M', 'متر', 'م', 'length', TRUE, 'وحدة الطول الأساسية', TRUE, 1),
('CM', 'سنتيمتر', 'سم', 'length', FALSE, 'وحدة الطول الفرعية', TRUE, 1);

-- إدراج المخازن الأساسية
INSERT INTO warehouses (warehouse_code, warehouse_name, warehouse_location, warehouse_type, manager_name, phone, branch_id, capacity, capacity_unit, is_active, allow_negative_stock, created_by) VALUES
('WH001', 'المخزن الرئيسي', 'المنطقة الصناعية، الرياض', 'main', 'خالد أحمد', '011-9876543', 1, 1000.00, 'متر مربع', TRUE, FALSE, 1),
('WH002', 'مخزن جدة', 'المنطقة الصناعية، جدة', 'branch', 'سعد محمد', '012-3456789', 2, 500.00, 'متر مربع', TRUE, FALSE, 1),
('WH003', 'مخزن مؤقت', 'مخزن للبضائع المؤقتة', 'temporary', 'علي حسن', '011-5555555', 1, 100.00, 'متر مربع', TRUE, TRUE, 1);

-- إدراج أصناف تجريبية
INSERT INTO items (item_code, item_name, item_description, category_id, base_unit_id, barcode, item_type, cost_method, min_stock_level, max_stock_level, reorder_level, is_active, has_expiry_date, created_by) VALUES
('ITM001', 'حليب كامل الدسم', 'حليب طازج كامل الدسم 1 لتر', 3, 3, '1234567890123', 'product', 'fifo', 50.000, 500.000, 100.000, TRUE, TRUE, 1),
('ITM002', 'أرز بسمتي', 'أرز بسمتي درجة أولى', 1, 1, '2345678901234', 'product', 'average', 100.000, 1000.000, 200.000, TRUE, FALSE, 1),
('ITM003', 'دجاج مجمد', 'دجاج كامل مجمد', 5, 1, '3456789012345', 'product', 'fifo', 20.000, 200.000, 50.000, TRUE, TRUE, 1),
('ITM004', 'عصير برتقال', 'عصير برتقال طبيعي 250 مل', 2, 4, '4567890123456', 'product', 'fifo', 100.000, 1000.000, 200.000, TRUE, TRUE, 1),
('ITM005', 'خبز أبيض', 'خبز أبيض طازج', 1, 5, '5678901234567', 'product', 'fifo', 50.000, 200.000, 100.000, TRUE, TRUE, 1);

-- إدراج وحدات الأصناف مع معاملات التحويل
INSERT INTO item_units (item_id, unit_id, conversion_factor, purchase_price, sale_price, wholesale_price, is_default, is_purchasable, is_saleable, created_by) VALUES
-- حليب كامل الدسم
(1, 3, 1.000000, 3.50, 4.00, 3.75, TRUE, TRUE, TRUE, 1),  -- لتر
(1, 4, 0.001000, 0.0035, 0.004, 0.00375, FALSE, FALSE, TRUE, 1),  -- مليلتر
(1, 6, 12.000000, 42.00, 48.00, 45.00, FALSE, TRUE, TRUE, 1),  -- صندوق (12 لتر)

-- أرز بسمتي
(2, 1, 1.000000, 8.00, 10.00, 9.00, TRUE, TRUE, TRUE, 1),  -- كيلوجرام
(2, 2, 0.001000, 0.008, 0.010, 0.009, FALSE, FALSE, TRUE, 1),  -- جرام
(2, 6, 25.000000, 200.00, 250.00, 225.00, FALSE, TRUE, TRUE, 1),  -- صندوق (25 كجم)

-- دجاج مجمد
(3, 1, 1.000000, 15.00, 18.00, 16.50, TRUE, TRUE, TRUE, 1),  -- كيلوجرام
(3, 2, 0.001000, 0.015, 0.018, 0.0165, FALSE, FALSE, TRUE, 1),  -- جرام

-- عصير برتقال
(4, 4, 1.000000, 1.50, 2.00, 1.75, TRUE, TRUE, TRUE, 1),  -- مليلتر (250 مل)
(4, 6, 24.000000, 36.00, 48.00, 42.00, FALSE, TRUE, TRUE, 1),  -- صندوق (24 قطعة)

-- خبز أبيض
(5, 5, 1.000000, 1.00, 1.50, 1.25, TRUE, TRUE, TRUE, 1),  -- قطعة
(5, 6, 20.000000, 20.00, 30.00, 25.00, FALSE, TRUE, TRUE, 1);  -- صندوق (20 قطعة)

-- إدراج أرصدة افتتاحية للمخزون
INSERT INTO inventory_balances (item_id, warehouse_id, unit_id, quantity, reserved_quantity, average_cost, last_cost, last_movement_date) VALUES
-- المخزن الرئيسي
(1, 1, 3, 200.000000, 0.000000, 3.50, 3.50, NOW()),  -- حليب - لتر
(2, 1, 1, 500.000000, 0.000000, 8.00, 8.00, NOW()),  -- أرز - كيلوجرام
(3, 1, 1, 100.000000, 0.000000, 15.00, 15.00, NOW()), -- دجاج - كيلوجرام
(4, 1, 4, 1000.000000, 0.000000, 1.50, 1.50, NOW()), -- عصير - مليلتر
(5, 1, 5, 150.000000, 0.000000, 1.00, 1.00, NOW()),  -- خبز - قطعة

-- مخزن جدة
(1, 2, 3, 100.000000, 0.000000, 3.50, 3.50, NOW()),  -- حليب - لتر
(2, 2, 1, 250.000000, 0.000000, 8.00, 8.00, NOW()),  -- أرز - كيلوجرام
(3, 2, 1, 50.000000, 0.000000, 15.00, 15.00, NOW()),  -- دجاج - كيلوجرام
(4, 2, 4, 500.000000, 0.000000, 1.50, 1.50, NOW()),  -- عصير - مليلتر
(5, 2, 5, 75.000000, 0.000000, 1.00, 1.00, NOW());   -- خبز - قطعة

-- إدراج حركات افتتاحية للمخزون
INSERT INTO inventory_movements (movement_number, movement_date, movement_type, item_id, warehouse_id, unit_id, quantity, unit_cost, reference_type, reference_number, notes, is_posted, posted_at, created_by) VALUES
-- حركات افتتاحية للمخزن الرئيسي
('OP-001', CURDATE(), 'opening_balance', 1, 1, 3, 200.000000, 3.50, 'opening', 'رصيد افتتاحي', 'رصيد افتتاحي للحليب', TRUE, NOW(), 1),
('OP-002', CURDATE(), 'opening_balance', 2, 1, 1, 500.000000, 8.00, 'opening', 'رصيد افتتاحي', 'رصيد افتتاحي للأرز', TRUE, NOW(), 1),
('OP-003', CURDATE(), 'opening_balance', 3, 1, 1, 100.000000, 15.00, 'opening', 'رصيد افتتاحي', 'رصيد افتتاحي للدجاج', TRUE, NOW(), 1),
('OP-004', CURDATE(), 'opening_balance', 4, 1, 4, 1000.000000, 1.50, 'opening', 'رصيد افتتاحي', 'رصيد افتتاحي للعصير', TRUE, NOW(), 1),
('OP-005', CURDATE(), 'opening_balance', 5, 1, 5, 150.000000, 1.00, 'opening', 'رصيد افتتاحي', 'رصيد افتتاحي للخبز', TRUE, NOW(), 1),

-- حركات افتتاحية لمخزن جدة
('OP-006', CURDATE(), 'opening_balance', 1, 2, 3, 100.000000, 3.50, 'opening', 'رصيد افتتاحي', 'رصيد افتتاحي للحليب - جدة', TRUE, NOW(), 1),
('OP-007', CURDATE(), 'opening_balance', 2, 2, 1, 250.000000, 8.00, 'opening', 'رصيد افتتاحي', 'رصيد افتتاحي للأرز - جدة', TRUE, NOW(), 1),
('OP-008', CURDATE(), 'opening_balance', 3, 2, 1, 50.000000, 15.00, 'opening', 'رصيد افتتاحي', 'رصيد افتتاحي للدجاج - جدة', TRUE, NOW(), 1),
('OP-009', CURDATE(), 'opening_balance', 4, 2, 4, 500.000000, 1.50, 'opening', 'رصيد افتتاحي', 'رصيد افتتاحي للعصير - جدة', TRUE, NOW(), 1),
('OP-010', CURDATE(), 'opening_balance', 5, 2, 5, 75.000000, 1.00, 'opening', 'رصيد افتتاحي', 'رصيد افتتاحي للخبز - جدة', TRUE, NOW(), 1);

-- إدراج قيود محاسبية تجريبية للأرصدة الافتتاحية
INSERT INTO account_transactions (voucher_number, transaction_date, account_id, debit_amount, credit_amount, description, reference_type, reference_number, branch_id, is_posted, posted_at, created_by) VALUES
-- قيد الرصيد الافتتاحي للمخزون
('V-001', CURDATE(), 10, 16200.00, 0.00, 'رصيد افتتاحي للمخزون - المخزن الرئيسي', 'opening', 'OP-INVENTORY-001', 1, TRUE, NOW(), 1),
('V-001', CURDATE(), 4, 0.00, 16200.00, 'رصيد افتتاحي للمخزون - المخزن الرئيسي', 'opening', 'OP-INVENTORY-001', 1, TRUE, NOW(), 1),

-- قيد الرصيد الافتتاحي لمخزن جدة
('V-002', CURDATE(), 10, 8100.00, 0.00, 'رصيد افتتاحي للمخزون - مخزن جدة', 'opening', 'OP-INVENTORY-002', 2, TRUE, NOW(), 1),
('V-002', CURDATE(), 4, 0.00, 8100.00, 'رصيد افتتاحي للمخزون - مخزن جدة', 'opening', 'OP-INVENTORY-002', 2, TRUE, NOW(), 1),

-- قيد نقدية افتتاحية
('V-003', CURDATE(), 8, 50000.00, 0.00, 'رصيد نقدي افتتاحي', 'opening', 'OP-CASH-001', 1, TRUE, NOW(), 1),
('V-003', CURDATE(), 4, 0.00, 50000.00, 'رصيد نقدي افتتاحي', 'opening', 'OP-CASH-001', 1, TRUE, NOW(), 1);

-- =====================================================
-- إنشاء Views مفيدة للتقارير
-- =====================================================

-- عرض تفصيلي للأصناف مع وحداتها
CREATE VIEW v_items_with_units AS
SELECT
    i.item_id,
    i.item_code,
    i.item_name,
    i.item_description,
    c.category_name,
    u.unit_name as base_unit_name,
    u.unit_symbol as base_unit_symbol,
    i.min_stock_level,
    i.max_stock_level,
    i.reorder_level,
    i.is_active,
    i.has_expiry_date,
    i.created_at
FROM items i
LEFT JOIN item_categories c ON i.category_id = c.category_id
LEFT JOIN units u ON i.base_unit_id = u.unit_id;

-- عرض أرصدة المخزون الحالية
CREATE VIEW v_current_inventory AS
SELECT
    i.item_code,
    i.item_name,
    w.warehouse_name,
    u.unit_name,
    u.unit_symbol,
    ib.quantity,
    ib.reserved_quantity,
    ib.available_quantity,
    ib.average_cost,
    ib.total_value,
    ib.last_movement_date,
    CASE
        WHEN ib.quantity <= i.min_stock_level THEN 'منخفض'
        WHEN ib.quantity >= i.max_stock_level THEN 'مرتفع'
        ELSE 'طبيعي'
    END as stock_status
FROM inventory_balances ib
JOIN items i ON ib.item_id = i.item_id
JOIN warehouses w ON ib.warehouse_id = w.warehouse_id
JOIN units u ON ib.unit_id = u.unit_id
WHERE i.is_active = TRUE AND w.is_active = TRUE;

-- عرض شجرة الحسابات مع الأرصدة
CREATE VIEW v_chart_of_accounts_with_balances AS
SELECT
    coa.account_id,
    coa.account_code,
    coa.account_name,
    coa.account_type,
    coa.account_level,
    coa.is_main_account,
    parent.account_name as parent_account_name,
    COALESCE(SUM(at.debit_amount), 0) as total_debit,
    COALESCE(SUM(at.credit_amount), 0) as total_credit,
    COALESCE(SUM(at.debit_amount) - SUM(at.credit_amount), 0) as balance,
    coa.is_active
FROM chart_of_accounts coa
LEFT JOIN chart_of_accounts parent ON coa.parent_account_id = parent.account_id
LEFT JOIN account_transactions at ON coa.account_id = at.account_id AND at.is_posted = TRUE
WHERE coa.is_active = TRUE
GROUP BY coa.account_id, coa.account_code, coa.account_name, coa.account_type,
         coa.account_level, coa.is_main_account, parent.account_name, coa.is_active;

-- =====================================================
-- إنشاء Stored Procedures مفيدة
-- =====================================================

DELIMITER //

-- إجراء لحساب إجمالي قيمة المخزون
CREATE PROCEDURE sp_calculate_total_inventory_value(
    IN p_warehouse_id INT,
    IN p_as_of_date DATE,
    OUT p_total_value DECIMAL(15,2)
)
BEGIN
    SELECT COALESCE(SUM(quantity * average_cost), 0)
    INTO p_total_value
    FROM inventory_balances ib
    JOIN items i ON ib.item_id = i.item_id
    WHERE (p_warehouse_id IS NULL OR ib.warehouse_id = p_warehouse_id)
    AND i.is_active = TRUE
    AND ib.last_movement_date <= p_as_of_date;
END //

-- إجراء لتحديث أرصدة المخزون بعد الحركة
CREATE PROCEDURE sp_update_inventory_balance(
    IN p_item_id INT,
    IN p_warehouse_id INT,
    IN p_unit_id INT,
    IN p_quantity DECIMAL(15,6),
    IN p_unit_cost DECIMAL(12,6),
    IN p_movement_type ENUM('in', 'out')
)
BEGIN
    DECLARE v_current_qty DECIMAL(15,6) DEFAULT 0;
    DECLARE v_current_cost DECIMAL(12,6) DEFAULT 0;
    DECLARE v_new_qty DECIMAL(15,6);
    DECLARE v_new_cost DECIMAL(12,6);

    -- الحصول على الرصيد الحالي
    SELECT quantity, average_cost
    INTO v_current_qty, v_current_cost
    FROM inventory_balances
    WHERE item_id = p_item_id AND warehouse_id = p_warehouse_id AND unit_id = p_unit_id;

    -- حساب الكمية والتكلفة الجديدة
    IF p_movement_type = 'in' THEN
        SET v_new_qty = v_current_qty + p_quantity;
        IF v_current_qty > 0 THEN
            SET v_new_cost = ((v_current_qty * v_current_cost) + (p_quantity * p_unit_cost)) / v_new_qty;
        ELSE
            SET v_new_cost = p_unit_cost;
        END IF;
    ELSE
        SET v_new_qty = v_current_qty - p_quantity;
        SET v_new_cost = v_current_cost;
    END IF;

    -- تحديث الرصيد
    INSERT INTO inventory_balances (item_id, warehouse_id, unit_id, quantity, average_cost, last_cost, last_movement_date)
    VALUES (p_item_id, p_warehouse_id, p_unit_id, v_new_qty, v_new_cost, p_unit_cost, NOW())
    ON DUPLICATE KEY UPDATE
        quantity = v_new_qty,
        average_cost = v_new_cost,
        last_cost = p_unit_cost,
        last_movement_date = NOW();
END //

DELIMITER ;

-- =====================================================
-- إنشاء Triggers للتحديث التلقائي
-- =====================================================

-- Trigger لتحديث أرصدة المخزون عند إدراج حركة جديدة
DELIMITER //
CREATE TRIGGER tr_inventory_movement_after_insert
AFTER INSERT ON inventory_movements
FOR EACH ROW
BEGIN
    IF NEW.is_posted = TRUE THEN
        CALL sp_update_inventory_balance(
            NEW.item_id,
            NEW.warehouse_id,
            NEW.unit_id,
            NEW.quantity,
            NEW.unit_cost,
            NEW.movement_type
        );
    END IF;
END //
DELIMITER ;

-- =====================================================
-- إنشاء فهارس إضافية لتحسين الأداء
-- =====================================================

-- فهارس مركبة لتحسين أداء الاستعلامات
CREATE INDEX idx_account_transactions_date_account ON account_transactions(transaction_date, account_id);
CREATE INDEX idx_inventory_movements_item_warehouse ON inventory_movements(item_id, warehouse_id, movement_date);
CREATE INDEX idx_inventory_balances_warehouse_item ON inventory_balances(warehouse_id, item_id);

-- =====================================================
-- رسائل النجاح
-- =====================================================
SELECT 'تم إنشاء قاعدة البيانات بنجاح!' as message;
SELECT 'تم إدراج البيانات التجريبية بنجاح!' as message;
SELECT 'النظام جاهز للاستخدام!' as message;
