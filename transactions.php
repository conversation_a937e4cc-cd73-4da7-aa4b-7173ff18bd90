<?php
/**
 * صفحة القيود المحاسبية
 * Accounting Transactions Page
 */

define('APP_INIT', true);
require_once 'includes/init.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('transactions_manage');

// إنشاء مثيلات النماذج
$transactionModel = new Transaction();
$accountModel = new Account();

// معالجة المرشحات
$dateFrom = sanitizeInput($_GET['date_from'] ?? '');
$dateTo = sanitizeInput($_GET['date_to'] ?? '');
$accountFilter = !empty($_GET['account']) ? (int)$_GET['account'] : null;
$searchTerm = sanitizeInput($_GET['search'] ?? '');
$page = max(1, (int)($_GET['page'] ?? 1));
$transactionsPerPage = 50;
$offset = ($page - 1) * $transactionsPerPage;

// الحصول على المعاملات
if (!empty($searchTerm)) {
    $transactions = $transactionModel->searchTransactions($searchTerm, $dateFrom, $dateTo);
    $totalTransactions = count($transactions);
} else {
    $transactions = $transactionModel->getAllTransactions($dateFrom, $dateTo, $accountFilter, $transactionsPerPage, $offset);
    
    // حساب إجمالي المعاملات للترقيم
    $allTransactions = $transactionModel->getAllTransactions($dateFrom, $dateTo, $accountFilter);
    $totalTransactions = count($allTransactions);
}

$totalPages = ceil($totalTransactions / $transactionsPerPage);

// تجميع المعاملات حسب رقم القيد
$groupedTransactions = [];
foreach ($transactions as $transaction) {
    $voucherNumber = $transaction['voucher_number'];
    if (!isset($groupedTransactions[$voucherNumber])) {
        $groupedTransactions[$voucherNumber] = [
            'voucher_number' => $voucherNumber,
            'transaction_date' => $transaction['transaction_date'],
            'is_posted' => $transaction['is_posted'],
            'created_at' => $transaction['created_at'],
            'transactions' => [],
            'total_debit' => 0,
            'total_credit' => 0
        ];
    }
    
    $groupedTransactions[$voucherNumber]['transactions'][] = $transaction;
    $groupedTransactions[$voucherNumber]['total_debit'] += $transaction['debit_amount'];
    $groupedTransactions[$voucherNumber]['total_credit'] += $transaction['credit_amount'];
}

// الحصول على قوائم المساعدة
$accounts = $accountModel->getAllAccounts();

$pageTitle = 'القيود المحاسبية';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- خط عربي -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- أيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .card-hover:hover { transform: translateY(-2px); transition: all 0.3s ease; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        
        /* تحسين عرض القيود */
        .voucher-group {
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            overflow: hidden;
        }
        
        .voucher-header {
            background-color: #f9fafb;
            padding: 1rem;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .voucher-transactions {
            background-color: white;
        }
        
        .transaction-row {
            border-bottom: 1px solid #f3f4f6;
        }
        
        .transaction-row:last-child {
            border-bottom: none;
        }
        
        .posted-voucher {
            border-right: 4px solid #10b981;
        }
        
        .unposted-voucher {
            border-right: 4px solid #f59e0b;
        }
    </style>
</head>
<body class="bg-gray-50">

    <!-- شريط التنقل العلوي -->
    <nav class="gradient-bg shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="dashboard.php" class="flex-shrink-0 flex items-center text-white hover:text-gray-200">
                        <i class="fas fa-arrow-right text-xl ml-3"></i>
                        <span>العودة للوحة التحكم</span>
                    </a>
                </div>
                
                <div class="flex items-center">
                    <h1 class="text-white text-xl font-bold">
                        <i class="fas fa-exchange-alt ml-2"></i>
                        <?php echo $pageTitle; ?>
                    </h1>
                </div>
                
                <div class="flex items-center space-x-4 space-x-reverse">
                    <span class="text-white text-sm">
                        <i class="fas fa-user ml-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </span>
                    <a href="logout.php" class="text-white hover:text-gray-200 transition-colors duration-200">
                        <i class="fas fa-sign-out-alt ml-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        
        <!-- عرض الرسائل -->
        <?php showAlert(); ?>
        
        <!-- القيود المحاسبية -->
        <div class="bg-white shadow-lg rounded-lg">
            
            <!-- رأس القائمة -->
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900">
                    <i class="fas fa-list ml-2"></i>
                    القيود المحاسبية (<?php echo count($groupedTransactions); ?> قيد)
                </h3>
                <div class="flex space-x-2 space-x-reverse">
                    <a href="trial_balance.php" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                        <i class="fas fa-balance-scale ml-2"></i>
                        ميزان المراجعة
                    </a>
                    <a href="accounts.php" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                        <i class="fas fa-chart-line ml-2"></i>
                        شجرة الحسابات
                    </a>
                    <a href="transactions.php?action=add" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                        <i class="fas fa-plus ml-2"></i>
                        قيد جديد
                    </a>
                </div>
            </div>
            
            <!-- شريط البحث والفلترة -->
            <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
                <form method="GET" action="transactions.php" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <div>
                        <input 
                            type="text" 
                            name="search" 
                            value="<?php echo htmlspecialchars($searchTerm); ?>"
                            placeholder="البحث (رقم القيد، الوصف، الحساب)"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                    </div>
                    <div>
                        <input 
                            type="date" 
                            name="date_from" 
                            value="<?php echo htmlspecialchars($dateFrom); ?>"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="من تاريخ"
                        >
                    </div>
                    <div>
                        <input 
                            type="date" 
                            name="date_to" 
                            value="<?php echo htmlspecialchars($dateTo); ?>"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="إلى تاريخ"
                        >
                    </div>
                    <div>
                        <select name="account" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">جميع الحسابات</option>
                            <?php foreach ($accounts as $account): ?>
                                <option value="<?php echo $account['account_id']; ?>" 
                                        <?php echo $accountFilter == $account['account_id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($account['account_code'] . ' - ' . $account['account_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="flex space-x-2 space-x-reverse">
                        <button type="submit" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-search ml-2"></i>
                            بحث
                        </button>
                        <?php if (!empty($searchTerm) || !empty($dateFrom) || !empty($dateTo) || $accountFilter): ?>
                            <a href="transactions.php" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-times"></i>
                            </a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>

            <!-- قائمة القيود -->
            <div class="p-6">
                <?php if (empty($groupedTransactions)): ?>
                    <div class="text-center py-12">
                        <i class="fas fa-exchange-alt text-gray-400 text-6xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد قيود محاسبية</h3>
                        <p class="text-gray-500 mb-4">لم يتم العثور على أي قيود مطابقة للبحث</p>
                        <a href="transactions.php?action=add" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-plus ml-2"></i>
                            إضافة أول قيد
                        </a>
                    </div>
                <?php else: ?>
                    <div class="space-y-4">
                        <?php foreach ($groupedTransactions as $voucher): ?>
                            <div class="voucher-group <?php echo $voucher['is_posted'] ? 'posted-voucher' : 'unposted-voucher'; ?>">

                                <!-- رأس القيد -->
                                <div class="voucher-header">
                                    <div class="flex justify-between items-center">
                                        <div class="flex items-center space-x-4 space-x-reverse">
                                            <h4 class="text-lg font-medium text-gray-900">
                                                قيد رقم: <?php echo htmlspecialchars($voucher['voucher_number']); ?>
                                            </h4>
                                            <span class="text-sm text-gray-500">
                                                <?php echo formatDate($voucher['transaction_date']); ?>
                                            </span>
                                            <?php if ($voucher['is_posted']): ?>
                                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                                    مرحل
                                                </span>
                                            <?php else: ?>
                                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                    غير مرحل
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            <div class="text-sm text-gray-600">
                                                <span class="font-medium">إجمالي:</span>
                                                <?php echo formatMoney($voucher['total_debit']); ?>
                                            </div>
                                            <div class="flex space-x-1 space-x-reverse">
                                                <a href="transactions.php?action=view&voucher=<?php echo urlencode($voucher['voucher_number']); ?>"
                                                   class="text-blue-600 hover:text-blue-800 p-1" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <?php if (!$voucher['is_posted']): ?>
                                                    <a href="transactions.php?action=edit&voucher=<?php echo urlencode($voucher['voucher_number']); ?>"
                                                       class="text-green-600 hover:text-green-800 p-1" title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="transactions.php?action=post&voucher=<?php echo urlencode($voucher['voucher_number']); ?>"
                                                       class="text-purple-600 hover:text-purple-800 p-1" title="ترحيل">
                                                        <i class="fas fa-check"></i>
                                                    </a>
                                                    <a href="transactions.php?action=delete&voucher=<?php echo urlencode($voucher['voucher_number']); ?>"
                                                       onclick="return confirm('هل أنت متأكد من حذف هذا القيد؟')"
                                                       class="text-red-600 hover:text-red-800 p-1" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                <?php else: ?>
                                                    <a href="transactions.php?action=unpost&voucher=<?php echo urlencode($voucher['voucher_number']); ?>"
                                                       onclick="return confirm('هل أنت متأكد من إلغاء ترحيل هذا القيد؟')"
                                                       class="text-orange-600 hover:text-orange-800 p-1" title="إلغاء الترحيل">
                                                        <i class="fas fa-undo"></i>
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- معاملات القيد -->
                                <div class="voucher-transactions">
                                    <table class="w-full">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">الحساب</th>
                                                <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">البيان</th>
                                                <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">مدين</th>
                                                <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 uppercase">دائن</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($voucher['transactions'] as $transaction): ?>
                                                <tr class="transaction-row">
                                                    <td class="px-4 py-3">
                                                        <div class="text-sm font-medium text-gray-900">
                                                            <?php echo htmlspecialchars($transaction['account_name']); ?>
                                                        </div>
                                                        <div class="text-xs text-gray-500">
                                                            <?php echo htmlspecialchars($transaction['account_code']); ?>
                                                        </div>
                                                    </td>
                                                    <td class="px-4 py-3 text-sm text-gray-700">
                                                        <?php echo htmlspecialchars($transaction['description']); ?>
                                                    </td>
                                                    <td class="px-4 py-3 text-sm font-medium text-gray-900">
                                                        <?php if ($transaction['debit_amount'] > 0): ?>
                                                            <?php echo formatMoney($transaction['debit_amount']); ?>
                                                        <?php else: ?>
                                                            <span class="text-gray-400">-</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td class="px-4 py-3 text-sm font-medium text-gray-900">
                                                        <?php if ($transaction['credit_amount'] > 0): ?>
                                                            <?php echo formatMoney($transaction['credit_amount']); ?>
                                                        <?php else: ?>
                                                            <span class="text-gray-400">-</span>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                        <tfoot class="bg-gray-50">
                                            <tr>
                                                <td colspan="2" class="px-4 py-2 text-sm font-medium text-gray-900">الإجمالي</td>
                                                <td class="px-4 py-2 text-sm font-bold text-gray-900">
                                                    <?php echo formatMoney($voucher['total_debit']); ?>
                                                </td>
                                                <td class="px-4 py-2 text-sm font-bold text-gray-900">
                                                    <?php echo formatMoney($voucher['total_credit']); ?>
                                                </td>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>

                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- ترقيم الصفحات -->
            <?php if ($totalPages > 1 && empty($searchTerm)): ?>
                <div class="px-6 py-4 border-t border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-700">
                            عرض <?php echo (($page - 1) * $transactionsPerPage) + 1; ?> إلى
                            <?php echo min($page * $transactionsPerPage, $totalTransactions); ?> من
                            <?php echo number_format($totalTransactions); ?> معاملة
                        </div>
                        <div class="flex space-x-2 space-x-reverse">
                            <?php if ($page > 1): ?>
                                <a href="transactions.php?page=<?php echo $page - 1; ?><?php echo !empty($dateFrom) ? '&date_from=' . urlencode($dateFrom) : ''; ?><?php echo !empty($dateTo) ? '&date_to=' . urlencode($dateTo) : ''; ?><?php echo $accountFilter ? '&account=' . $accountFilter : ''; ?>"
                                   class="px-3 py-2 text-sm bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition-colors duration-200">
                                    السابق
                                </a>
                            <?php endif; ?>

                            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                <a href="transactions.php?page=<?php echo $i; ?><?php echo !empty($dateFrom) ? '&date_from=' . urlencode($dateFrom) : ''; ?><?php echo !empty($dateTo) ? '&date_to=' . urlencode($dateTo) : ''; ?><?php echo $accountFilter ? '&account=' . $accountFilter : ''; ?>"
                                   class="px-3 py-2 text-sm <?php echo $i == $page ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-700 hover:bg-gray-400'; ?> rounded transition-colors duration-200">
                                    <?php echo $i; ?>
                                </a>
                            <?php endfor; ?>

                            <?php if ($page < $totalPages): ?>
                                <a href="transactions.php?page=<?php echo $page + 1; ?><?php echo !empty($dateFrom) ? '&date_from=' . urlencode($dateFrom) : ''; ?><?php echo !empty($dateTo) ? '&date_to=' . urlencode($dateTo) : ''; ?><?php echo $accountFilter ? '&account=' . $accountFilter : ''; ?>"
                                   class="px-3 py-2 text-sm bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition-colors duration-200">
                                    التالي
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

        </div>

    </div>

    <!-- تحسينات CSS للتفاعل -->
    <style>
        /* تحسين مظهر الجداول */
        table tr:hover {
            background-color: #f9fafb;
        }

        /* تحسين مظهر الأزرار */
        button:hover, a:hover {
            transform: translateY(-1px);
        }

        /* تحسين عرض القيود */
        .voucher-group:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        /* تحسين عرض الجداول على الشاشات الصغيرة */
        @media (max-width: 768px) {
            .voucher-header {
                padding: 0.75rem;
            }

            .voucher-header h4 {
                font-size: 1rem;
            }

            table {
                font-size: 0.75rem;
            }

            table th,
            table td {
                padding: 0.5rem;
            }
        }
    </style>

</body>
</html>
