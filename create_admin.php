<?php
/**
 * سكريبت إنشاء مستخدم مدير
 * Create Admin User Script
 */

define('APP_INIT', true);
require_once 'includes/init.php';

$message = '';
$messageType = '';
$adminCreated = false;

// التحقق من وجود مدير في النظام
$db = Database::getInstance();
$existingAdmin = $db->selectOne("SELECT * FROM users WHERE role = 'admin' LIMIT 1"); 

// معالجة إنشاء المدير
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_admin'])) {
    $username = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    $fullName = sanitizeInput($_POST['full_name'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    
    // التحقق من البيانات
    if (empty($username) || empty($password) || empty($fullName) || empty($email)) {
        $message = 'جميع الحقول مطلوبة';
        $messageType = 'error';
    } elseif ($password !== $confirmPassword) {
        $message = 'كلمة المرور وتأكيدها غير متطابقتين';
        $messageType = 'error';
    } elseif (strlen($password) < 6) {
        $message = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
        $messageType = 'error';
    } else {
        // التحقق من عدم وجود اسم المستخدم
        $existingUser = $db->selectOne("SELECT user_id FROM users WHERE username = ?", [$username]);
        
        if ($existingUser) {
            $message = 'اسم المستخدم موجود مسبقاً';
            $messageType = 'error';
        } else {
            // إنشاء المستخدم المدير
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
            
            $query = "INSERT INTO users (
                        username, password, full_name, email, role, 
                        is_active, created_at, created_by
                      ) VALUES (?, ?, ?, ?, 'admin', 1, NOW(), 0)";
            
            $params = [$username, $hashedPassword, $fullName, $email];
            
            $userId = $db->insert($query, $params);
            
            if ($userId) {
                $message = 'تم إنشاء المستخدم المدير بنجاح';
                $messageType = 'success';
                $adminCreated = true;
                
                // تسجيل النشاط
                $activityQuery = "INSERT INTO activity_log (
                                    user_id, action, description, ip_address, created_at
                                  ) VALUES (?, 'إنشاء مدير', ?, ?, NOW())";
                
                $db->insert($activityQuery, [
                    $userId,
                    "تم إنشاء مستخدم مدير جديد: $fullName ($username)",
                    $_SERVER['REMOTE_ADDR'] ?? 'unknown'
                ]);
            } else {
                $message = 'فشل في إنشاء المستخدم المدير';
                $messageType = 'error';
            }
        }
    }
}

$pageTitle = 'إنشاء مستخدم مدير';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- خط عربي -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- أيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    </style>
</head>
<body class="bg-gray-50">

    <!-- شريط التنقل العلوي -->
    <nav class="gradient-bg shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-center h-16">
                <div class="flex items-center">
                    <h1 class="text-white text-xl font-bold">
                        <i class="fas fa-user-shield ml-2"></i>
                        <?php echo $pageTitle; ?>
                    </h1>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="max-w-md mx-auto py-12">
        
        <!-- عرض الرسائل -->
        <?php if (!empty($message)): ?>
            <div class="mb-6">
                <div class="<?php echo $messageType === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700'; ?> px-4 py-3 rounded">
                    <i class="fas <?php echo $messageType === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?> ml-2"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            </div>
        <?php endif; ?>
        
        <?php if ($adminCreated): ?>
            <!-- نجح إنشاء المدير -->
            <div class="bg-white shadow-lg rounded-lg p-8 text-center">
                <i class="fas fa-check-circle text-green-500 text-6xl mb-4"></i>
                <h2 class="text-2xl font-bold text-gray-900 mb-4">تم إنشاء المدير بنجاح!</h2>
                <p class="text-gray-600 mb-6">يمكنك الآن تسجيل الدخول باستخدام البيانات التي أدخلتها</p>
                <a href="login.php" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors duration-200">
                    <i class="fas fa-sign-in-alt ml-2"></i>
                    تسجيل الدخول
                </a>
            </div>
            
        <?php elseif ($existingAdmin): ?>
            <!-- يوجد مدير بالفعل -->
            <div class="bg-white shadow-lg rounded-lg p-8">
                <div class="text-center mb-6">
                    <i class="fas fa-info-circle text-blue-500 text-6xl mb-4"></i>
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">يوجد مدير في النظام</h2>
                    <p class="text-gray-600">يوجد مستخدم مدير في النظام بالفعل</p>
                </div>
                
                <div class="bg-gray-50 rounded-lg p-4 mb-6">
                    <h3 class="font-semibold text-gray-900 mb-2">بيانات المدير الحالي:</h3>
                    <p><strong>اسم المستخدم:</strong> <?php echo htmlspecialchars($existingAdmin['username']); ?></p>
                    <p><strong>الاسم الكامل:</strong> <?php echo htmlspecialchars($existingAdmin['full_name']); ?></p>
                    <p><strong>البريد الإلكتروني:</strong> <?php echo htmlspecialchars($existingAdmin['email']); ?></p>
                    <p><strong>تاريخ الإنشاء:</strong> <?php echo formatDateTime($existingAdmin['created_at']); ?></p>
                </div>
                
                <div class="text-center">
                    <a href="login.php" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors duration-200">
                        <i class="fas fa-sign-in-alt ml-2"></i>
                        تسجيل الدخول
                    </a>
                </div>
            </div>
            
        <?php else: ?>
            <!-- نموذج إنشاء المدير -->
            <div class="bg-white shadow-lg rounded-lg p-8">
                <div class="text-center mb-6">
                    <i class="fas fa-user-plus text-blue-500 text-6xl mb-4"></i>
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">إنشاء مستخدم مدير</h2>
                    <p class="text-gray-600">قم بإنشاء أول مستخدم مدير للنظام</p>
                </div>
                
                <form method="POST" action="">
                    
                    <!-- اسم المستخدم -->
                    <div class="mb-4">
                        <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                            اسم المستخدم <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="username" name="username" required
                               value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <!-- الاسم الكامل -->
                    <div class="mb-4">
                        <label for="full_name" class="block text-sm font-medium text-gray-700 mb-2">
                            الاسم الكامل <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="full_name" name="full_name" required
                               value="<?php echo htmlspecialchars($_POST['full_name'] ?? ''); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <!-- البريد الإلكتروني -->
                    <div class="mb-4">
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                            البريد الإلكتروني <span class="text-red-500">*</span>
                        </label>
                        <input type="email" id="email" name="email" required
                               value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <!-- كلمة المرور -->
                    <div class="mb-4">
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                            كلمة المرور <span class="text-red-500">*</span>
                        </label>
                        <input type="password" id="password" name="password" required minlength="6"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <p class="text-xs text-gray-500 mt-1">يجب أن تكون 6 أحرف على الأقل</p>
                    </div>
                    
                    <!-- تأكيد كلمة المرور -->
                    <div class="mb-6">
                        <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-2">
                            تأكيد كلمة المرور <span class="text-red-500">*</span>
                        </label>
                        <input type="password" id="confirm_password" name="confirm_password" required minlength="6"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <!-- زر الإنشاء -->
                    <button type="submit" name="create_admin" value="1" 
                            class="w-full bg-blue-500 hover:bg-blue-600 text-white px-4 py-3 rounded-lg transition-colors duration-200">
                        <i class="fas fa-user-plus ml-2"></i>
                        إنشاء المستخدم المدير
                    </button>
                    
                </form>
                
                <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div class="flex">
                        <i class="fas fa-exclamation-triangle text-yellow-400 ml-2"></i>
                        <div>
                            <h4 class="text-sm font-medium text-yellow-800">تنبيه مهم</h4>
                            <p class="text-sm text-yellow-700 mt-1">
                                احتفظ ببيانات تسجيل الدخول في مكان آمن. ستحتاجها للوصول إلى النظام.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
        
    </div>

</body>
</html>
