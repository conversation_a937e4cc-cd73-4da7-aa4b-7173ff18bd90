<?php
/**
 * صفحة تحميل المرفقات
 * Download Attachment Page
 */

define('APP_INIT', true);
require_once 'includes/init.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('attachments_view');

// إنشاء مثيل نموذج المرفقات
$attachmentModel = new Attachment();

// الحصول على معرف المرفق
$attachmentId = $_GET['id'] ?? null;

if (!$attachmentId) {
    header('Location: attachments.php?error=attachment_not_found');
    exit;
}

// الحصول على بيانات المرفق
$attachment = $attachmentModel->getAttachmentById($attachmentId);

if (!$attachment) {
    header('Location: attachments.php?error=attachment_not_found');
    exit;
}

// التحقق من وجود الملف
if (!file_exists($attachment['file_path'])) {
    header('Location: attachments.php?error=file_not_found');
    exit;
}

// تسجيل عملية التحميل
logActivity('تحميل مرفق', "تم تحميل المرفق: {$attachment['original_name']}");

// إعداد headers للتحميل
header('Content-Type: ' . $attachment['mime_type']);
header('Content-Disposition: attachment; filename="' . $attachment['original_name'] . '"');
header('Content-Length: ' . $attachment['file_size']);
header('Cache-Control: no-cache, must-revalidate');
header('Expires: 0');

// قراءة وإرسال الملف
readfile($attachment['file_path']);
exit;

?>
