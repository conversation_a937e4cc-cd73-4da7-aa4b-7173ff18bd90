<?php
/**
 * كشف حساب العميل
 * Customer Statement Page
 */

define('APP_INIT', true);
require_once 'includes/init.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('reports_view');

// إنشاء مثيلات النماذج
$reportModel = new Report();
$customerModel = new Customer();

// معالجة الفلاتر
$customerId = !empty($_GET['customer_id']) ? (int)$_GET['customer_id'] : null;
$filters = [
    'date_from' => sanitizeInput($_GET['date_from'] ?? ''),
    'date_to' => sanitizeInput($_GET['date_to'] ?? '')
];

// الحصول على البيانات
$customers = $customerModel->getAllCustomers(true);
$customerData = null;
$statementData = [];
$customerBalance = [];

if ($customerId) {
    $customerData = $customerModel->getCustomerById($customerId);
    $statementData = $reportModel->getCustomerStatement($customerId, $filters);
    $customerBalance = $reportModel->getCustomerBalance($customerId, $filters['date_to'] ?? null);
}

$pageTitle = 'كشف حساب العميل';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- خط عربي -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- أيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .card-hover:hover { transform: translateY(-2px); transition: all 0.3s ease; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        
        @media print {
            .no-print { display: none !important; }
            .print-full-width { width: 100% !important; }
            body { background: white !important; }
        }
    </style>
</head>
<body class="bg-gray-50">

    <!-- شريط التنقل العلوي -->
    <nav class="gradient-bg shadow-lg no-print">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="reports.php" class="flex-shrink-0 flex items-center text-white hover:text-gray-200">
                        <i class="fas fa-arrow-right text-xl ml-3"></i>
                        <span>العودة للتقارير</span>
                    </a>
                </div>
                
                <div class="flex items-center">
                    <h1 class="text-white text-xl font-bold">
                        <i class="fas fa-file-invoice ml-2"></i>
                        <?php echo $pageTitle; ?>
                    </h1>
                </div>
                
                <div class="flex items-center space-x-4 space-x-reverse">
                    <!-- مكون الإشعارات -->
                    <?php include 'includes/notification_widget.php'; ?>
                    
                    <div class="text-white text-sm">
                        <i class="fas fa-user ml-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </div>
                    <a href="logout.php" class="text-white hover:text-gray-200 transition-colors duration-200">
                        <i class="fas fa-sign-out-alt ml-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        
        <!-- فلاتر التقرير -->
        <div class="bg-white shadow-lg rounded-lg mb-8 no-print">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">
                    <i class="fas fa-filter ml-2"></i>
                    اختيار العميل والفترة
                </h3>
            </div>
            
            <div class="p-6">
                <form method="GET" action="customer_statement.php" class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    
                    <!-- العميل -->
                    <div>
                        <label for="customer_id" class="block text-sm font-medium text-gray-700 mb-2">
                            العميل <span class="text-red-500">*</span>
                        </label>
                        <select id="customer_id" name="customer_id" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">اختر العميل</option>
                            <?php foreach ($customers as $customer): ?>
                                <option value="<?php echo $customer['customer_id']; ?>" 
                                        <?php echo $customerId == $customer['customer_id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($customer['customer_name']); ?>
                                    <?php if (!empty($customer['customer_code'])): ?>
                                        (<?php echo htmlspecialchars($customer['customer_code']); ?>)
                                    <?php endif; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <!-- من تاريخ -->
                    <div>
                        <label for="date_from" class="block text-sm font-medium text-gray-700 mb-2">
                            من تاريخ
                        </label>
                        <input type="date" id="date_from" name="date_from" 
                               value="<?php echo htmlspecialchars($filters['date_from']); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <!-- إلى تاريخ -->
                    <div>
                        <label for="date_to" class="block text-sm font-medium text-gray-700 mb-2">
                            إلى تاريخ
                        </label>
                        <input type="date" id="date_to" name="date_to" 
                               value="<?php echo htmlspecialchars($filters['date_to']); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <!-- أزرار الإجراءات -->
                    <div class="md:col-span-3 flex space-x-4 space-x-reverse">
                        <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-search ml-2"></i>
                            عرض كشف الحساب
                        </button>
                        <a href="customer_statement.php" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-times ml-2"></i>
                            مسح الفلاتر
                        </a>
                        <?php if ($customerId): ?>
                            <button type="button" onclick="window.print()" class="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-print ml-2"></i>
                                طباعة
                            </button>
                        <?php endif; ?>
                    </div>
                    
                </form>
            </div>
        </div>
        
        <?php if ($customerId && $customerData): ?>
            
            <!-- معلومات العميل -->
            <div class="bg-white shadow-lg rounded-lg mb-8 print-full-width">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-user ml-2"></i>
                        معلومات العميل
                    </h3>
                </div>
                
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-500">اسم العميل</label>
                            <p class="mt-1 text-sm text-gray-900"><?php echo htmlspecialchars($customerData['customer_name']); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500">كود العميل</label>
                            <p class="mt-1 text-sm text-gray-900"><?php echo htmlspecialchars($customerData['customer_code'] ?? 'غير محدد'); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500">الهاتف</label>
                            <p class="mt-1 text-sm text-gray-900"><?php echo htmlspecialchars($customerData['phone'] ?? 'غير محدد'); ?></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500">البريد الإلكتروني</label>
                            <p class="mt-1 text-sm text-gray-900"><?php echo htmlspecialchars($customerData['email'] ?? 'غير محدد'); ?></p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- ملخص الرصيد -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                
                <!-- إجمالي المبيعات -->
                <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-shopping-cart text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="mr-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">إجمالي المبيعات</dt>
                                    <dd class="text-lg font-medium text-gray-900">
                                        <?php echo formatMoney($customerBalance['total_sales'] ?? 0); ?>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- إجمالي المرتجعات -->
                <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-undo text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="mr-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">إجمالي المرتجعات</dt>
                                    <dd class="text-lg font-medium text-gray-900">
                                        <?php echo formatMoney($customerBalance['total_returns'] ?? 0); ?>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- الرصيد الحالي -->
                <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 <?php echo ($customerBalance['balance'] ?? 0) >= 0 ? 'bg-green-500' : 'bg-orange-500'; ?> rounded-full flex items-center justify-center">
                                    <i class="fas fa-balance-scale text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="mr-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">الرصيد الحالي</dt>
                                    <dd class="text-lg font-medium <?php echo ($customerBalance['balance'] ?? 0) >= 0 ? 'text-green-600' : 'text-orange-600'; ?>">
                                        <?php echo formatMoney(abs($customerBalance['balance'] ?? 0)); ?>
                                        <span class="text-xs">
                                            (<?php echo ($customerBalance['balance'] ?? 0) >= 0 ? 'مدين' : 'دائن'; ?>)
                                        </span>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
                
            </div>

            <!-- جدول كشف الحساب -->
            <div class="bg-white shadow-lg rounded-lg print-full-width">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">
                            <i class="fas fa-table ml-2"></i>
                            كشف حساب العميل (<?php echo count($statementData); ?> حركة)
                        </h3>
                        <div class="text-sm text-gray-500">
                            <?php if (!empty($filters['date_from']) || !empty($filters['date_to'])): ?>
                                الفترة:
                                <?php if (!empty($filters['date_from'])): ?>
                                    من <?php echo formatDate($filters['date_from']); ?>
                                <?php endif; ?>
                                <?php if (!empty($filters['date_to'])): ?>
                                    إلى <?php echo formatDate($filters['date_to']); ?>
                                <?php endif; ?>
                            <?php else: ?>
                                جميع الحركات
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <?php if (empty($statementData)): ?>
                        <div class="text-center py-12">
                            <i class="fas fa-file-invoice text-gray-400 text-6xl mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد حركات</h3>
                            <p class="text-gray-500">لا توجد حركات مالية لهذا العميل في الفترة المحددة</p>
                        </div>
                    <?php else: ?>
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        التاريخ
                                    </th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        نوع الحركة
                                    </th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        رقم المرجع
                                    </th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        الوصف
                                    </th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        مدين
                                    </th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        دائن
                                    </th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        الرصيد
                                    </th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider no-print">
                                        إجراءات
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php
                                $runningBalance = 0;
                                foreach ($statementData as $transaction):
                                    $runningBalance += $transaction['debit_amount'] - $transaction['credit_amount'];

                                    // تحديد نوع الحركة
                                    $transactionTypeText = '';
                                    $transactionIcon = '';
                                    $transactionColor = '';

                                    if ($transaction['transaction_type'] === 'invoice') {
                                        if ($transaction['debit_amount'] > 0) {
                                            $transactionTypeText = 'فاتورة مبيعات';
                                            $transactionIcon = 'fas fa-file-invoice';
                                            $transactionColor = 'text-blue-600';
                                        } else {
                                            $transactionTypeText = 'مرتجع مبيعات';
                                            $transactionIcon = 'fas fa-undo';
                                            $transactionColor = 'text-red-600';
                                        }
                                    } else {
                                        $transactionTypeText = 'دفعة';
                                        $transactionIcon = 'fas fa-money-bill';
                                        $transactionColor = 'text-green-600';
                                    }
                                ?>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php echo formatDate($transaction['transaction_date']); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <div class="flex items-center">
                                                <i class="<?php echo $transactionIcon; ?> <?php echo $transactionColor; ?> ml-2"></i>
                                                <span><?php echo $transactionTypeText; ?></span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            <?php echo htmlspecialchars($transaction['reference_number']); ?>
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-900">
                                            <?php echo htmlspecialchars($transaction['description'] ?? ''); ?>
                                            <?php if ($transaction['transaction_type'] === 'invoice' && !empty($transaction['due_date'])): ?>
                                                <div class="text-xs text-gray-500">
                                                    تاريخ الاستحقاق: <?php echo formatDate($transaction['due_date']); ?>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php if ($transaction['debit_amount'] > 0): ?>
                                                <span class="text-red-600 font-medium">
                                                    <?php echo formatMoney($transaction['debit_amount']); ?>
                                                </span>
                                            <?php else: ?>
                                                -
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php if ($transaction['credit_amount'] > 0): ?>
                                                <span class="text-green-600 font-medium">
                                                    <?php echo formatMoney($transaction['credit_amount']); ?>
                                                </span>
                                            <?php else: ?>
                                                -
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <span class="<?php echo $runningBalance >= 0 ? 'text-red-600' : 'text-green-600'; ?>">
                                                <?php echo formatMoney(abs($runningBalance)); ?>
                                                <span class="text-xs">
                                                    (<?php echo $runningBalance >= 0 ? 'مدين' : 'دائن'; ?>)
                                                </span>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium no-print">
                                            <?php if ($transaction['transaction_type'] === 'invoice'): ?>
                                                <a href="invoices.php?action=view&id=<?php echo $transaction['reference_id']; ?>"
                                                   class="text-blue-600 hover:text-blue-900" title="عرض الفاتورة">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                            <tfoot class="bg-gray-50">
                                <tr>
                                    <td colspan="4" class="px-6 py-4 text-sm font-medium text-gray-900">
                                        الإجمالي (<?php echo count($statementData); ?> حركة)
                                    </td>
                                    <td class="px-6 py-4 text-sm font-medium text-red-600">
                                        <?php echo formatMoney(array_sum(array_column($statementData, 'debit_amount'))); ?>
                                    </td>
                                    <td class="px-6 py-4 text-sm font-medium text-green-600">
                                        <?php echo formatMoney(array_sum(array_column($statementData, 'credit_amount'))); ?>
                                    </td>
                                    <td class="px-6 py-4 text-sm font-medium <?php echo $runningBalance >= 0 ? 'text-red-600' : 'text-green-600'; ?>">
                                        <?php echo formatMoney(abs($runningBalance)); ?>
                                        <span class="text-xs">
                                            (<?php echo $runningBalance >= 0 ? 'مدين' : 'دائن'; ?>)
                                        </span>
                                    </td>
                                    <td class="px-6 py-4"></td>
                                </tr>
                            </tfoot>
                        </table>
                    <?php endif; ?>
                </div>
            </div>

        <?php elseif ($customerId): ?>

            <!-- رسالة عدم وجود عميل -->
            <div class="bg-white shadow-lg rounded-lg">
                <div class="text-center py-12">
                    <i class="fas fa-user-times text-gray-400 text-6xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">العميل غير موجود</h3>
                    <p class="text-gray-500">لم يتم العثور على العميل المحدد</p>
                </div>
            </div>

        <?php else: ?>

            <!-- رسالة اختيار عميل -->
            <div class="bg-white shadow-lg rounded-lg">
                <div class="text-center py-12">
                    <i class="fas fa-user-plus text-gray-400 text-6xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">اختر عميل</h3>
                    <p class="text-gray-500">يرجى اختيار عميل من القائمة أعلاه لعرض كشف الحساب</p>
                </div>
            </div>

        <?php endif; ?>

    </div>

</body>
</html>
