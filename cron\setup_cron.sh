#!/bin/bash

# إعداد Cron Jobs للنسخ الاحتياطي التلقائي
# Setup Cron Jobs for Automatic Backup

# الحصول على المسار الحالي
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

echo "إعداد Cron Jobs للنسخ الاحتياطي التلقائي..."
echo "مسار المشروع: $PROJECT_DIR"

# التحقق من وجود PHP
if ! command -v php &> /dev/null; then
    echo "خطأ: PHP غير مثبت أو غير موجود في PATH"
    exit 1
fi

# إنشاء مجلد السجلات إذا لم يكن موجوداً
mkdir -p "$PROJECT_DIR/logs"

# إنشاء ملف crontab مؤقت
TEMP_CRON=$(mktemp)

# الحصول على crontab الحالي (إن وجد)
crontab -l 2>/dev/null > "$TEMP_CRON" || true

# إضافة تعليق للتعرف على cron jobs الخاصة بالنظام
echo "" >> "$TEMP_CRON"
echo "# النسخ الاحتياطي التلقائي - Automatic Backup System" >> "$TEMP_CRON"

# النسخ الاحتياطي اليومي (كل يوم في الساعة 2:00 صباحاً)
echo "0 2 * * * /usr/bin/php $SCRIPT_DIR/backup_scheduler.php run >> $PROJECT_DIR/logs/backup_cron.log 2>&1" >> "$TEMP_CRON"

# فحص حالة النظام (كل 6 ساعات)
echo "0 */6 * * * /usr/bin/php $SCRIPT_DIR/backup_scheduler.php health >> $PROJECT_DIR/logs/health_check.log 2>&1" >> "$TEMP_CRON"

# فحص الإشعارات (كل ساعة)
echo "0 * * * * /usr/bin/php $SCRIPT_DIR/notification_checker.php run >> $PROJECT_DIR/logs/notification_check.log 2>&1" >> "$TEMP_CRON"

# فحص المخزون المنخفض (كل 4 ساعات)
echo "0 */4 * * * /usr/bin/php $SCRIPT_DIR/notification_checker.php low_stock >> $PROJECT_DIR/logs/low_stock_check.log 2>&1" >> "$TEMP_CRON"

# فحص الفواتير المستحقة (مرتين يومياً)
echo "0 9,17 * * * /usr/bin/php $SCRIPT_DIR/notification_checker.php overdue >> $PROJECT_DIR/logs/overdue_check.log 2>&1" >> "$TEMP_CRON"

# تنظيف السجلات القديمة (كل أسبوع يوم الأحد في الساعة 3:00 صباحاً)
echo "0 3 * * 0 find $PROJECT_DIR/logs -name '*.log' -mtime +30 -delete" >> "$TEMP_CRON"

echo "" >> "$TEMP_CRON"

# تطبيق crontab الجديد
crontab "$TEMP_CRON"

# حذف الملف المؤقت
rm "$TEMP_CRON"

echo "تم إعداد Cron Jobs بنجاح!"
echo ""
echo "المهام المجدولة:"
echo "- نسخة احتياطية يومية: كل يوم في الساعة 2:00 صباحاً"
echo "- فحص حالة النظام: كل 6 ساعات"
echo "- فحص الإشعارات: كل ساعة"
echo "- فحص المخزون المنخفض: كل 4 ساعات"
echo "- فحص الفواتير المستحقة: مرتين يومياً (9:00 صباحاً و 5:00 مساءً)"
echo "- تنظيف السجلات القديمة: كل أسبوع يوم الأحد في الساعة 3:00 صباحاً"
echo ""
echo "لعرض المهام المجدولة الحالية:"
echo "crontab -l"
echo ""
echo "لحذف جميع المهام المجدولة:"
echo "crontab -r"
echo ""
echo "ملفات السجلات:"
echo "- سجل النسخ الاحتياطي: $PROJECT_DIR/logs/backup_cron.log"
echo "- سجل فحص الحالة: $PROJECT_DIR/logs/health_check.log"
echo "- سجل فحص الإشعارات: $PROJECT_DIR/logs/notification_check.log"
echo "- سجل فحص المخزون: $PROJECT_DIR/logs/low_stock_check.log"
echo "- سجل فحص الفواتير: $PROJECT_DIR/logs/overdue_check.log"
echo "- سجل المجدول: $PROJECT_DIR/logs/backup_scheduler.log"
echo "- سجل مجدول الإشعارات: $PROJECT_DIR/logs/notification_checker.log"
