<?php
/**
 * صفحة الإعدادات المتقدمة
 * Advanced Settings Page
 */

define('APP_INIT', true);
require_once 'includes/init.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('system_admin');

// إنشاء مثيل نموذج الإعدادات
$settingModel = new Setting();

// معالجة العمليات
$action = $_GET['action'] ?? 'list';
$message = '';
$messageType = '';

// معالجة تصدير الإعدادات
if ($action === 'export') {
    $exportData = $settingModel->exportSettings();
    
    if ($exportData) {
        $filename = 'settings_export_' . date('Y-m-d_H-i-s') . '.json';
        
        header('Content-Type: application/json');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Length: ' . strlen($exportData));
        
        echo $exportData;
        exit;
    } else {
        $message = 'فشل في تصدير الإعدادات';
        $messageType = 'error';
    }
}

// معالجة استيراد الإعدادات
if ($action === 'import' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    if (verifyCSRFToken($csrfToken)) {
        if (isset($_FILES['import_file']) && $_FILES['import_file']['error'] === UPLOAD_ERR_OK) {
            $uploadedFile = $_FILES['import_file'];
            $fileContent = file_get_contents($uploadedFile['tmp_name']);
            
            if ($settingModel->importSettings($fileContent)) {
                $message = 'تم استيراد الإعدادات بنجاح';
                $messageType = 'success';
            } else {
                $message = 'فشل في استيراد الإعدادات. تأكد من صحة ملف JSON';
                $messageType = 'error';
            }
        } else {
            $message = 'يرجى اختيار ملف للاستيراد';
            $messageType = 'error';
        }
    } else {
        $message = 'طلب غير صالح';
        $messageType = 'error';
    }
}

// معالجة إعادة تعيين الإعدادات
if ($action === 'reset' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    if (verifyCSRFToken($csrfToken)) {
        // حذف جميع الإعدادات الحالية
        $db = Database::getInstance();
        $db->delete("DELETE FROM settings");
        
        // إعادة تهيئة الإعدادات الافتراضية
        if ($settingModel->initializeDefaultSettings()) {
            $message = 'تم إعادة تعيين الإعدادات إلى القيم الافتراضية';
            $messageType = 'success';
            
            // مسح الكاش
            Setting::clearCache();
        } else {
            $message = 'فشل في إعادة تعيين الإعدادات';
            $messageType = 'error';
        }
    } else {
        $message = 'طلب غير صالح';
        $messageType = 'error';
    }
}

// معالجة حذف إعداد محدد
if ($action === 'delete' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrfToken = $_POST['csrf_token'] ?? '';
    $settingKey = $_POST['setting_key'] ?? '';
    
    if (verifyCSRFToken($csrfToken) && !empty($settingKey)) {
        if ($settingModel->delete($settingKey)) {
            $message = "تم حذف الإعداد: {$settingKey}";
            $messageType = 'success';
        } else {
            $message = 'فشل في حذف الإعداد';
            $messageType = 'error';
        }
    } else {
        $message = 'طلب غير صالح';
        $messageType = 'error';
    }
}

// معالجة إضافة/تحديث إعداد
if ($action === 'save' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrfToken = $_POST['csrf_token'] ?? '';
    $settingKey = sanitizeInput($_POST['setting_key'] ?? '');
    $settingValue = sanitizeInput($_POST['setting_value'] ?? '');
    $settingDescription = sanitizeInput($_POST['setting_description'] ?? '');
    
    if (verifyCSRFToken($csrfToken) && !empty($settingKey)) {
        if ($settingModel->set($settingKey, $settingValue, $settingDescription)) {
            $message = "تم حفظ الإعداد: {$settingKey}";
            $messageType = 'success';
        } else {
            $message = 'فشل في حفظ الإعداد';
            $messageType = 'error';
        }
    } else {
        $message = 'يرجى ملء جميع الحقول المطلوبة';
        $messageType = 'error';
    }
}

// الحصول على جميع الإعدادات
$allSettings = $settingModel->getAllSettings();

$pageTitle = 'الإعدادات المتقدمة';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- خط عربي -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- أيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    </style>
</head>
<body class="bg-gray-50">

    <!-- شريط التنقل العلوي -->
    <nav class="gradient-bg shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="settings.php" class="flex-shrink-0 flex items-center text-white hover:text-gray-200">
                        <i class="fas fa-arrow-right text-xl ml-3"></i>
                        <span>العودة للإعدادات</span>
                    </a>
                </div>
                
                <div class="flex items-center">
                    <h1 class="text-white text-xl font-bold">
                        <i class="fas fa-cogs ml-2"></i>
                        <?php echo $pageTitle; ?>
                    </h1>
                </div>
                
                <div class="flex items-center space-x-4 space-x-reverse">
                    <!-- مكون الإشعارات -->
                    <?php include 'includes/notification_widget.php'; ?>
                    
                    <div class="text-white text-sm">
                        <i class="fas fa-user ml-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </div>
                    <a href="logout.php" class="text-white hover:text-gray-200 transition-colors duration-200">
                        <i class="fas fa-sign-out-alt ml-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        
        <!-- عرض الرسائل -->
        <?php if (!empty($message)): ?>
            <div class="mb-6">
                <div class="<?php echo $messageType === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700'; ?> px-4 py-3 rounded">
                    <i class="fas <?php echo $messageType === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?> ml-2"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- أدوات الإدارة -->
        <div class="bg-white shadow-lg rounded-lg mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">
                    <i class="fas fa-tools ml-2"></i>
                    أدوات إدارة الإعدادات
                </h3>
            </div>
            
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    
                    <!-- تصدير الإعدادات -->
                    <div class="text-center">
                        <a href="?action=export" class="inline-flex items-center px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors duration-200">
                            <i class="fas fa-download ml-2"></i>
                            تصدير الإعدادات
                        </a>
                        <p class="text-sm text-gray-500 mt-2">تحميل جميع الإعدادات كملف JSON</p>
                    </div>
                    
                    <!-- استيراد الإعدادات -->
                    <div class="text-center">
                        <form method="POST" action="?action=import" enctype="multipart/form-data" class="inline-block">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <div class="mb-2">
                                <input type="file" name="import_file" accept=".json" required class="hidden" id="import_file">
                                <label for="import_file" class="inline-flex items-center px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg cursor-pointer transition-colors duration-200">
                                    <i class="fas fa-upload ml-2"></i>
                                    اختيار ملف
                                </label>
                            </div>
                            <button type="submit" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-200">
                                <i class="fas fa-file-import ml-2"></i>
                                استيراد
                            </button>
                        </form>
                        <p class="text-sm text-gray-500 mt-2">استيراد الإعدادات من ملف JSON</p>
                    </div>
                    
                    <!-- إعادة تعيين الإعدادات -->
                    <div class="text-center">
                        <form method="POST" action="?action=reset" class="inline-block">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <button type="submit" onclick="return confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟ سيتم فقدان جميع التخصيصات!')" 
                                    class="inline-flex items-center px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors duration-200">
                                <i class="fas fa-undo ml-2"></i>
                                إعادة تعيين
                            </button>
                        </form>
                        <p class="text-sm text-gray-500 mt-2">إعادة تعيين جميع الإعدادات للقيم الافتراضية</p>
                    </div>
                    
                </div>
            </div>
        </div>

        <!-- إضافة/تحديث إعداد -->
        <div class="bg-white shadow-lg rounded-lg mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">
                    <i class="fas fa-plus ml-2"></i>
                    إضافة/تحديث إعداد
                </h3>
            </div>

            <div class="p-6">
                <form method="POST" action="?action=save">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label for="setting_key" class="block text-sm font-medium text-gray-700 mb-2">
                                مفتاح الإعداد <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="setting_key" name="setting_key" required
                                   placeholder="مثال: company_name"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <div>
                            <label for="setting_value" class="block text-sm font-medium text-gray-700 mb-2">
                                قيمة الإعداد <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="setting_value" name="setting_value" required
                                   placeholder="قيمة الإعداد"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <div>
                            <label for="setting_description" class="block text-sm font-medium text-gray-700 mb-2">
                                وصف الإعداد
                            </label>
                            <input type="text" id="setting_description" name="setting_description"
                                   placeholder="وصف اختياري للإعداد"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>

                    <div class="mt-6 flex justify-end">
                        <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-save ml-2"></i>
                            حفظ الإعداد
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- قائمة جميع الإعدادات -->
        <div class="bg-white shadow-lg rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">
                    <i class="fas fa-list ml-2"></i>
                    جميع الإعدادات (<?php echo count($allSettings); ?> إعداد)
                </h3>
            </div>

            <div class="overflow-x-auto">
                <?php if (empty($allSettings)): ?>
                    <div class="text-center py-12">
                        <i class="fas fa-cog text-gray-400 text-6xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد إعدادات</h3>
                        <p class="text-gray-500">لم يتم العثور على أي إعدادات في النظام</p>
                    </div>
                <?php else: ?>
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    مفتاح الإعداد
                                </th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    القيمة
                                </th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    الوصف
                                </th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    تاريخ الإنشاء
                                </th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    آخر تحديث
                                </th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    إجراءات
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($allSettings as $key => $setting): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        <?php echo htmlspecialchars($setting['setting_key']); ?>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-900">
                                        <div class="max-w-xs truncate" title="<?php echo htmlspecialchars($setting['setting_value']); ?>">
                                            <?php echo htmlspecialchars($setting['setting_value']); ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-500">
                                        <div class="max-w-xs truncate" title="<?php echo htmlspecialchars($setting['setting_description'] ?? ''); ?>">
                                            <?php echo htmlspecialchars($setting['setting_description'] ?? 'لا يوجد وصف'); ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php echo formatDateTime($setting['created_at']); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php echo $setting['updated_at'] ? formatDateTime($setting['updated_at']) : '-'; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="editSetting('<?php echo htmlspecialchars($setting['setting_key']); ?>', '<?php echo htmlspecialchars($setting['setting_value']); ?>', '<?php echo htmlspecialchars($setting['setting_description'] ?? ''); ?>')"
                                                class="text-blue-600 hover:text-blue-900 ml-3" title="تحرير">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <form method="POST" action="?action=delete" class="inline">
                                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                            <input type="hidden" name="setting_key" value="<?php echo htmlspecialchars($setting['setting_key']); ?>">
                                            <button type="submit" onclick="return confirm('هل أنت متأكد من حذف هذا الإعداد؟')"
                                                    class="text-red-600 hover:text-red-900" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>

    </div>

    <!-- JavaScript -->
    <script>
        function editSetting(key, value, description) {
            document.getElementById('setting_key').value = key;
            document.getElementById('setting_value').value = value;
            document.getElementById('setting_description').value = description;

            // التمرير إلى نموذج التحرير
            document.querySelector('form[action="?action=save"]').scrollIntoView({ behavior: 'smooth' });

            // تركيز على حقل القيمة
            document.getElementById('setting_value').focus();
        }

        // معاينة اسم الملف المختار للاستيراد
        document.getElementById('import_file').addEventListener('change', function() {
            const fileName = this.files[0]?.name || 'لم يتم اختيار ملف';
            const label = this.nextElementSibling;
            label.innerHTML = '<i class="fas fa-file ml-2"></i>' + fileName;
        });
    </script>

</body>
</html>
