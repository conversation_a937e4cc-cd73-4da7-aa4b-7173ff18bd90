<?php
/**
 * صفحة إدارة العملاء والموردين
 * Customers and Suppliers Management Page
 */

define('APP_INIT', true);
require_once 'includes/init.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('customers_manage');

// إنشاء مثيلات النماذج
$customerModel = new Customer();

// معالجة العمليات
$action = $_GET['action'] ?? 'list';
$customerId = $_GET['id'] ?? null;
$error = '';
$success = '';

// معالجة الحذف
if ($action === 'delete' && $customerId) {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $csrfToken = $_POST['csrf_token'] ?? '';
        
        if (verifyCSRFToken($csrfToken)) {
            try {
                if ($customerModel->deleteCustomer($customerId)) {
                    setAlert('تم حذف العميل بنجاح', 'success');
                } else {
                    setAlert('فشل في حذف العميل', 'error');
                }
            } catch (Exception $e) {
                setAlert($e->getMessage(), 'error');
            }
        } else {
            setAlert('طلب غير صالح', 'error');
        }
        
        redirect('customers.php');
    }
}

// معالجة إضافة/تعديل العميل
if (($action === 'add' || $action === 'edit') && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    if (verifyCSRFToken($csrfToken)) {
        $data = [
            'customer_code' => sanitizeInput($_POST['customer_code'] ?? ''),
            'customer_name' => sanitizeInput($_POST['customer_name'] ?? ''),
            'customer_type' => sanitizeInput($_POST['customer_type'] ?? ''),
            'phone' => sanitizeInput($_POST['phone'] ?? ''),
            'email' => sanitizeInput($_POST['email'] ?? ''),
            'address' => sanitizeInput($_POST['address'] ?? ''),
            'city' => sanitizeInput($_POST['city'] ?? ''),
            'tax_number' => sanitizeInput($_POST['tax_number'] ?? ''),
            'credit_limit' => floatval($_POST['credit_limit'] ?? 0),
            'payment_terms' => intval($_POST['payment_terms'] ?? 30),
            'is_active' => isset($_POST['is_active']) ? 1 : 0
        ];
        
        // التحقق من البيانات المطلوبة
        if (empty($data['customer_code']) || empty($data['customer_name']) || empty($data['customer_type'])) {
            $error = 'يرجى ملء جميع الحقول المطلوبة';
        } else {
            try {
                if ($action === 'add') {
                    $newCustomerId = $customerModel->createCustomer($data);
                    if ($newCustomerId) {
                        setAlert('تم إضافة العميل بنجاح', 'success');
                        redirect('customers.php');
                    } else {
                        $error = 'فشل في إضافة العميل';
                    }
                } else {
                    if ($customerModel->updateCustomer($customerId, $data)) {
                        setAlert('تم تحديث العميل بنجاح', 'success');
                        redirect('customers.php');
                    } else {
                        $error = 'فشل في تحديث العميل';
                    }
                }
            } catch (Exception $e) {
                $error = $e->getMessage();
            }
        }
    } else {
        $error = 'طلب غير صالح';
    }
}

// الحصول على البيانات للعرض
$currentCustomer = null;
if ($action === 'edit' && $customerId) {
    $currentCustomer = $customerModel->getCustomerById($customerId);
    if (!$currentCustomer) {
        setAlert('العميل غير موجود', 'error');
        redirect('customers.php');
    }
}

// الحصول على قائمة العملاء للعرض
$searchTerm = sanitizeInput($_GET['search'] ?? '');
$typeFilter = sanitizeInput($_GET['type'] ?? '');

if (!empty($searchTerm)) {
    $customers = $customerModel->searchCustomers($searchTerm, $typeFilter);
} else {
    $customers = $customerModel->getAllCustomers($typeFilter);
}

// الحصول على الإحصائيات
$customersStats = $customerModel->getCustomersStats();
$customerTypes = $customerModel->getCustomerTypes();

$pageTitle = $action === 'add' ? 'إضافة عميل جديد' : 
            ($action === 'edit' ? 'تعديل العميل' : 'إدارة العملاء والموردين');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- خط عربي -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- أيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .card-hover:hover { transform: translateY(-2px); transition: all 0.3s ease; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        
        /* تحسين عرض البطاقات */
        .customer-card {
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .customer-type-customer { border-right: 4px solid #3b82f6; }
        .customer-type-supplier { border-right: 4px solid #10b981; }
        .customer-type-both { border-right: 4px solid #f59e0b; }
        
        .balance-positive { color: #059669; }
        .balance-negative { color: #dc2626; }
    </style>
</head>
<body class="bg-gray-50">

    <!-- شريط التنقل العلوي -->
    <nav class="gradient-bg shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="dashboard.php" class="flex-shrink-0 flex items-center text-white hover:text-gray-200">
                        <i class="fas fa-arrow-right text-xl ml-3"></i>
                        <span>العودة للوحة التحكم</span>
                    </a>
                </div>
                
                <div class="flex items-center">
                    <h1 class="text-white text-xl font-bold">
                        <i class="fas fa-users ml-2"></i>
                        <?php echo $pageTitle; ?>
                    </h1>
                </div>
                
                <div class="flex items-center space-x-4 space-x-reverse">
                    <span class="text-white text-sm">
                        <i class="fas fa-user ml-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </span>
                    <a href="logout.php" class="text-white hover:text-gray-200 transition-colors duration-200">
                        <i class="fas fa-sign-out-alt ml-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        
        <!-- عرض الرسائل -->
        <?php showAlert(); ?>
        
        <?php if (!empty($error)): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                <i class="fas fa-exclamation-triangle ml-2"></i>
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($action === 'list'): ?>
            <!-- بطاقات الإحصائيات -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                
                <!-- إجمالي العملاء -->
                <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-users text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="mr-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">إجمالي العملاء</dt>
                                    <dd class="text-lg font-medium text-gray-900">
                                        <?php echo number_format($customersStats['total_customers']); ?>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- العملاء فقط -->
                <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="mr-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">العملاء</dt>
                                    <dd class="text-lg font-medium text-gray-900">
                                        <?php echo number_format($customersStats['total_customers_only']); ?>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- الموردين -->
                <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-truck text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="mr-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">الموردين</dt>
                                    <dd class="text-lg font-medium text-gray-900">
                                        <?php echo number_format($customersStats['total_suppliers']); ?>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- النشطين -->
                <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-check-circle text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="mr-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">النشطين</dt>
                                    <dd class="text-lg font-medium text-gray-900">
                                        <?php echo number_format($customersStats['active_customers']); ?>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
                
            </div>

            <!-- قائمة العملاء -->
            <div class="bg-white shadow-lg rounded-lg">

                <!-- رأس القائمة -->
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-list ml-2"></i>
                        قائمة العملاء والموردين (<?php echo count($customers); ?> عميل)
                    </h3>
                    <div class="flex space-x-2 space-x-reverse">
                        <a href="invoices.php" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-file-invoice ml-2"></i>
                            الفواتير
                        </a>
                        <a href="customers.php?action=add" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-plus ml-2"></i>
                            إضافة عميل جديد
                        </a>
                    </div>
                </div>

                <!-- شريط البحث والفلترة -->
                <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
                    <form method="GET" action="customers.php" class="flex flex-wrap gap-4">
                        <div class="flex-1 min-w-64">
                            <input
                                type="text"
                                name="search"
                                value="<?php echo htmlspecialchars($searchTerm); ?>"
                                placeholder="البحث في العملاء (الاسم، الكود، الهاتف)"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                        </div>
                        <div class="min-w-48">
                            <select name="type" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">جميع الأنواع</option>
                                <?php foreach ($customerTypes as $type => $label): ?>
                                    <option value="<?php echo $type; ?>"
                                            <?php echo $typeFilter === $type ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($label); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <button type="submit" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-search ml-2"></i>
                            بحث
                        </button>
                        <?php if (!empty($searchTerm) || !empty($typeFilter)): ?>
                            <a href="customers.php" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-times ml-2"></i>
                                مسح
                            </a>
                        <?php endif; ?>
                    </form>
                </div>

                <!-- جدول العملاء -->
                <div class="overflow-x-auto">
                    <?php if (empty($customers)): ?>
                        <div class="text-center py-12">
                            <i class="fas fa-users text-gray-400 text-6xl mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">لا يوجد عملاء</h3>
                            <p class="text-gray-500 mb-4">لم يتم العثور على أي عملاء مطابقين للبحث</p>
                            <a href="customers.php?action=add" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-plus ml-2"></i>
                                إضافة أول عميل
                            </a>
                        </div>
                    <?php else: ?>
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">العميل</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">النوع</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">معلومات الاتصال</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الرصيد</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحد الائتماني</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php foreach ($customers as $customer): ?>
                                    <tr class="hover:bg-gray-50 customer-type-<?php echo $customer['customer_type']; ?>">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900">
                                                        <?php echo htmlspecialchars($customer['customer_name']); ?>
                                                    </div>
                                                    <div class="text-sm text-gray-500">
                                                        كود: <?php echo htmlspecialchars($customer['customer_code']); ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <?php
                                            $typeColors = [
                                                'customer' => 'bg-blue-100 text-blue-800',
                                                'supplier' => 'bg-green-100 text-green-800',
                                                'both' => 'bg-yellow-100 text-yellow-800'
                                            ];
                                            ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $typeColors[$customer['customer_type']] ?? 'bg-gray-100 text-gray-800'; ?>">
                                                <?php echo $customerTypes[$customer['customer_type']] ?? $customer['customer_type']; ?>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <div>
                                                <?php if (!empty($customer['phone'])): ?>
                                                    <div class="flex items-center">
                                                        <i class="fas fa-phone text-gray-400 ml-1"></i>
                                                        <?php echo htmlspecialchars($customer['phone']); ?>
                                                    </div>
                                                <?php endif; ?>
                                                <?php if (!empty($customer['email'])): ?>
                                                    <div class="flex items-center">
                                                        <i class="fas fa-envelope text-gray-400 ml-1"></i>
                                                        <?php echo htmlspecialchars($customer['email']); ?>
                                                    </div>
                                                <?php endif; ?>
                                                <?php if (!empty($customer['city'])): ?>
                                                    <div class="flex items-center">
                                                        <i class="fas fa-map-marker-alt text-gray-400 ml-1"></i>
                                                        <?php echo htmlspecialchars($customer['city']); ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php if ($customer['balance'] != 0): ?>
                                                <div class="font-medium <?php echo $customer['balance'] > 0 ? 'balance-positive' : 'balance-negative'; ?>">
                                                    <?php echo formatMoney(abs($customer['balance'])); ?>
                                                    <span class="text-xs">
                                                        (<?php echo $customer['balance'] > 0 ? 'له' : 'عليه'; ?>)
                                                    </span>
                                                </div>
                                            <?php else: ?>
                                                <span class="text-gray-400">متوازن</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php if ($customer['credit_limit'] > 0): ?>
                                                <?php echo formatMoney($customer['credit_limit']); ?>
                                            <?php else: ?>
                                                <span class="text-gray-400">غير محدد</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <?php if ($customer['is_active']): ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                    نشط
                                                </span>
                                            <?php else: ?>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                    غير نشط
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2 space-x-reverse">
                                                <a href="invoices.php?customer=<?php echo $customer['customer_id']; ?>"
                                                   class="text-purple-600 hover:text-purple-900 transition-colors duration-200"
                                                   title="عرض الفواتير">
                                                    <i class="fas fa-file-invoice"></i>
                                                </a>
                                                <a href="customers.php?action=edit&id=<?php echo $customer['customer_id']; ?>"
                                                   class="text-blue-600 hover:text-blue-900 transition-colors duration-200"
                                                   title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="customers.php?action=delete&id=<?php echo $customer['customer_id']; ?>"
                                                   onclick="return confirm('هل أنت متأكد من حذف العميل \'<?php echo htmlspecialchars($customer['customer_name'], ENT_QUOTES); ?>\'؟')"
                                                   class="text-red-600 hover:text-red-900 transition-colors duration-200"
                                                   title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>

            </div>

        <?php elseif ($action === 'add' || $action === 'edit'): ?>
            <!-- نموذج إضافة/تعديل العميل -->
            <div class="bg-white shadow-lg rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-<?php echo $action === 'add' ? 'plus' : 'edit'; ?> ml-2"></i>
                        <?php echo $action === 'add' ? 'إضافة عميل جديد' : 'تعديل العميل'; ?>
                    </h3>
                </div>

                <form method="POST" action="" class="p-6">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

                        <!-- كود العميل -->
                        <div>
                            <label for="customer_code" class="block text-sm font-medium text-gray-700 mb-2">
                                كود العميل <span class="text-red-500">*</span>
                            </label>
                            <input
                                type="text"
                                id="customer_code"
                                name="customer_code"
                                value="<?php echo htmlspecialchars($currentCustomer['customer_code'] ?? ''); ?>"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                                <?php echo $action === 'edit' ? 'readonly' : ''; ?>
                                placeholder="مثال: C001"
                            >
                        </div>

                        <!-- اسم العميل -->
                        <div>
                            <label for="customer_name" class="block text-sm font-medium text-gray-700 mb-2">
                                اسم العميل <span class="text-red-500">*</span>
                            </label>
                            <input
                                type="text"
                                id="customer_name"
                                name="customer_name"
                                value="<?php echo htmlspecialchars($currentCustomer['customer_name'] ?? ''); ?>"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                                placeholder="اسم العميل أو الشركة"
                            >
                        </div>

                        <!-- نوع العميل -->
                        <div>
                            <label for="customer_type" class="block text-sm font-medium text-gray-700 mb-2">
                                نوع العميل <span class="text-red-500">*</span>
                            </label>
                            <select
                                id="customer_type"
                                name="customer_type"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                            >
                                <option value="">اختر نوع العميل</option>
                                <?php foreach ($customerTypes as $type => $label): ?>
                                    <option value="<?php echo $type; ?>"
                                            <?php echo ($currentCustomer['customer_type'] ?? '') === $type ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($label); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- رقم الهاتف -->
                        <div>
                            <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                                رقم الهاتف
                            </label>
                            <input
                                type="tel"
                                id="phone"
                                name="phone"
                                value="<?php echo htmlspecialchars($currentCustomer['phone'] ?? ''); ?>"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                placeholder="رقم الهاتف"
                            >
                        </div>

                        <!-- البريد الإلكتروني -->
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                البريد الإلكتروني
                            </label>
                            <input
                                type="email"
                                id="email"
                                name="email"
                                value="<?php echo htmlspecialchars($currentCustomer['email'] ?? ''); ?>"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                placeholder="البريد الإلكتروني"
                            >
                        </div>

                        <!-- المدينة -->
                        <div>
                            <label for="city" class="block text-sm font-medium text-gray-700 mb-2">
                                المدينة
                            </label>
                            <input
                                type="text"
                                id="city"
                                name="city"
                                value="<?php echo htmlspecialchars($currentCustomer['city'] ?? ''); ?>"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                placeholder="المدينة"
                            >
                        </div>

                        <!-- الرقم الضريبي -->
                        <div>
                            <label for="tax_number" class="block text-sm font-medium text-gray-700 mb-2">
                                الرقم الضريبي
                            </label>
                            <input
                                type="text"
                                id="tax_number"
                                name="tax_number"
                                value="<?php echo htmlspecialchars($currentCustomer['tax_number'] ?? ''); ?>"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                placeholder="الرقم الضريبي"
                            >
                        </div>

                        <!-- الحد الائتماني -->
                        <div>
                            <label for="credit_limit" class="block text-sm font-medium text-gray-700 mb-2">
                                الحد الائتماني
                            </label>
                            <input
                                type="number"
                                id="credit_limit"
                                name="credit_limit"
                                value="<?php echo $currentCustomer['credit_limit'] ?? 0; ?>"
                                step="0.01"
                                min="0"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                placeholder="0.00"
                            >
                        </div>

                        <!-- شروط الدفع -->
                        <div>
                            <label for="payment_terms" class="block text-sm font-medium text-gray-700 mb-2">
                                شروط الدفع (بالأيام)
                            </label>
                            <input
                                type="number"
                                id="payment_terms"
                                name="payment_terms"
                                value="<?php echo $currentCustomer['payment_terms'] ?? 30; ?>"
                                min="0"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                placeholder="30"
                            >
                        </div>

                    </div>

                    <!-- العنوان -->
                    <div class="mt-6">
                        <label for="address" class="block text-sm font-medium text-gray-700 mb-2">
                            العنوان التفصيلي
                        </label>
                        <textarea
                            id="address"
                            name="address"
                            rows="3"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="العنوان التفصيلي للعميل"
                        ><?php echo htmlspecialchars($currentCustomer['address'] ?? ''); ?></textarea>
                    </div>

                    <!-- الخيارات الإضافية -->
                    <div class="mt-6">
                        <h4 class="text-md font-medium text-gray-900 mb-4">خيارات إضافية</h4>
                        <div class="space-y-3">

                            <div class="flex items-center">
                                <input
                                    type="checkbox"
                                    id="is_active"
                                    name="is_active"
                                    value="1"
                                    <?php echo ($currentCustomer['is_active'] ?? 1) ? 'checked' : ''; ?>
                                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                >
                                <label for="is_active" class="mr-2 block text-sm text-gray-900">
                                    العميل نشط
                                </label>
                            </div>

                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="mt-8 flex justify-end space-x-4 space-x-reverse">
                        <a href="customers.php" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-times ml-2"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-save ml-2"></i>
                            <?php echo $action === 'add' ? 'إضافة العميل' : 'حفظ التغييرات'; ?>
                        </button>
                    </div>

                </form>
            </div>

        <?php endif; ?>

    </div>

    <!-- تحسينات CSS للتفاعل -->
    <style>
        /* تحسين مظهر النماذج */
        input:focus, select:focus, textarea:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* تحسين مظهر الجداول */
        table tr:hover {
            background-color: #f9fafb;
        }

        /* تحسين مظهر الأزرار */
        button:hover, a:hover {
            transform: translateY(-1px);
        }

        /* تحسين عرض البطاقات */
        .customer-card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
    </style>

</body>
</html>
