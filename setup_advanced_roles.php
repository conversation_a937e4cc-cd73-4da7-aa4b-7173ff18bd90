<?php
/**
 * سكريبت إعداد الأدوار المتقدمة
 * Advanced Roles Setup Script
 */

define('APP_INIT', true);
require_once 'includes/init.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('system_admin');

// إنشاء مثيل نموذج الأدوار المتقدم
$advancedRoleModel = new AdvancedRole();

$message = '';
$messageType = '';

// معالجة إنشاء الأدوار الافتراضية
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_default_roles'])) {
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    if (verifyCSRFToken($csrfToken)) {
        if ($advancedRoleModel->createDefaultRoles()) {
            $message = 'تم إنشاء الأدوار الافتراضية بنجاح';
            $messageType = 'success';
        } else {
            $message = 'فشل في إنشاء الأدوار الافتراضية';
            $messageType = 'error';
        }
    } else {
        $message = 'طلب غير صالح';
        $messageType = 'error';
    }
}

// الحصول على الأدوار الحالية
$existingRoles = $advancedRoleModel->getAllRoles(true);

$pageTitle = 'إعداد الأدوار المتقدمة';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- خط عربي -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- أيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        
        .setup-card {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 20px;
        }
        
        .role-preview {
            background-color: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 16px;
            margin-bottom: 16px;
        }
        
        .permission-list {
            background-color: #f3f4f6;
            border-radius: 4px;
            padding: 8px;
            margin-top: 8px;
            font-size: 12px;
            color: #6b7280;
        }
    </style>
</head>
<body class="bg-gray-50">

    <!-- شريط التنقل العلوي -->
    <nav class="gradient-bg shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="dashboard.php" class="flex-shrink-0 flex items-center text-white hover:text-gray-200">
                        <i class="fas fa-arrow-right text-xl ml-3"></i>
                        <span>العودة للوحة التحكم</span>
                    </a>
                </div>
                
                <div class="flex items-center">
                    <h1 class="text-white text-xl font-bold">
                        <i class="fas fa-cogs ml-2"></i>
                        <?php echo $pageTitle; ?>
                    </h1>
                </div>
                
                <div class="flex items-center space-x-4 space-x-reverse">
                    <a href="advanced_roles.php" class="text-white hover:text-gray-200 transition-colors duration-200">
                        <i class="fas fa-user-shield ml-1"></i>
                        إدارة الأدوار
                    </a>
                    
                    <div class="text-white text-sm">
                        <i class="fas fa-user ml-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </div>
                    <a href="logout.php" class="text-white hover:text-gray-200 transition-colors duration-200">
                        <i class="fas fa-sign-out-alt ml-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        
        <!-- عرض الرسائل -->
        <?php if (!empty($message)): ?>
            <div class="mb-6">
                <div class="<?php echo $messageType === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700'; ?> px-4 py-3 rounded">
                    <i class="fas <?php echo $messageType === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?> ml-2"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            </div>
        <?php endif; ?>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            
            <!-- إعداد الأدوار الافتراضية -->
            <div class="bg-white shadow-lg rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-magic ml-2"></i>
                        إنشاء الأدوار الافتراضية
                    </h3>
                </div>
                
                <div class="p-6">
                    <p class="text-gray-600 mb-6">
                        سيتم إنشاء مجموعة من الأدوار الافتراضية مع صلاحياتها المناسبة لتسهيل إدارة النظام.
                    </p>
                    
                    <!-- معاينة الأدوار التي سيتم إنشاؤها -->
                    <div class="space-y-4 mb-6">
                        
                        <div class="role-preview">
                            <h4 class="font-semibold text-gray-900 mb-2">
                                <i class="fas fa-crown text-yellow-500 ml-2"></i>
                                مدير النظام
                            </h4>
                            <p class="text-sm text-gray-600 mb-2">صلاحيات كاملة على جميع وحدات النظام</p>
                            <div class="permission-list">
                                صلاحيات: جميع الوحدات • جميع العمليات • جميع الحقول • جميع البيانات
                            </div>
                        </div>
                        
                        <div class="role-preview">
                            <h4 class="font-semibold text-gray-900 mb-2">
                                <i class="fas fa-calculator text-blue-500 ml-2"></i>
                                محاسب
                            </h4>
                            <p class="text-sm text-gray-600 mb-2">صلاحيات المحاسبة والتقارير المالية</p>
                            <div class="permission-list">
                                صلاحيات: المحاسبة • الفواتير • التقارير • العملاء والموردين (قراءة)
                            </div>
                        </div>
                        
                        <div class="role-preview">
                            <h4 class="font-semibold text-gray-900 mb-2">
                                <i class="fas fa-warehouse text-green-500 ml-2"></i>
                                أمين المخزن
                            </h4>
                            <p class="text-sm text-gray-600 mb-2">صلاحيات إدارة المخزون والأصناف</p>
                            <div class="permission-list">
                                صلاحيات: المخزون • الأصناف • المخازن • حركات المخزون
                            </div>
                        </div>
                        
                        <div class="role-preview">
                            <h4 class="font-semibold text-gray-900 mb-2">
                                <i class="fas fa-handshake text-purple-500 ml-2"></i>
                                مندوب مبيعات
                            </h4>
                            <p class="text-sm text-gray-600 mb-2">صلاحيات المبيعات والعملاء</p>
                            <div class="permission-list">
                                صلاحيات: العملاء • فواتير المبيعات • إنشاء فواتير مبيعات
                            </div>
                        </div>
                        
                        <div class="role-preview">
                            <h4 class="font-semibold text-gray-900 mb-2">
                                <i class="fas fa-user text-gray-500 ml-2"></i>
                                مستخدم عادي
                            </h4>
                            <p class="text-sm text-gray-600 mb-2">صلاحيات أساسية للاستعلام والعرض</p>
                            <div class="permission-list">
                                صلاحيات: لوحة التحكم • التقارير الأساسية (قراءة فقط)
                            </div>
                        </div>
                        
                    </div>
                    
                    <!-- نموذج إنشاء الأدوار -->
                    <form method="POST" action="">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                            <div class="flex">
                                <i class="fas fa-exclamation-triangle text-yellow-400 ml-2"></i>
                                <div>
                                    <h4 class="text-sm font-medium text-yellow-800">تنبيه مهم</h4>
                                    <p class="text-sm text-yellow-700 mt-1">
                                        سيتم إنشاء الأدوار الافتراضية مع صلاحياتها. إذا كانت الأدوار موجودة مسبقاً، فلن يتم تعديلها.
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <button type="submit" name="create_default_roles" value="1" 
                                class="w-full bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors duration-200">
                            <i class="fas fa-magic ml-2"></i>
                            إنشاء الأدوار الافتراضية
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- الأدوار الحالية -->
            <div class="bg-white shadow-lg rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-list ml-2"></i>
                        الأدوار الحالية
                    </h3>
                </div>
                
                <div class="p-6">
                    <?php if (empty($existingRoles)): ?>
                        <div class="text-center py-8">
                            <i class="fas fa-user-shield text-gray-400 text-4xl mb-4"></i>
                            <p class="text-gray-500">لا توجد أدوار مُنشأة بعد</p>
                        </div>
                    <?php else: ?>
                        <div class="space-y-3">
                            <?php foreach ($existingRoles as $role): ?>
                                <div class="flex justify-between items-center p-3 border border-gray-200 rounded-lg">
                                    <div>
                                        <h4 class="font-medium text-gray-900"><?php echo htmlspecialchars($role['role_name']); ?></h4>
                                        <p class="text-sm text-gray-600"><?php echo htmlspecialchars($role['role_description']); ?></p>
                                    </div>
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            <?php 
                                            echo $role['role_type'] === 'system' ? 'bg-yellow-100 text-yellow-800' : 
                                                ($role['role_type'] === 'functional' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800');
                                            ?>">
                                            <?php 
                                            $typeLabels = ['system' => 'نظام', 'functional' => 'وظيفي', 'custom' => 'مخصص'];
                                            echo $typeLabels[$role['role_type']] ?? $role['role_type'];
                                            ?>
                                        </span>
                                        <span class="text-sm text-gray-500">
                                            <?php echo $role['users_count']; ?> مستخدم
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <div class="mt-6 text-center">
                            <a href="advanced_roles.php" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-cog ml-2"></i>
                                إدارة الأدوار
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
        </div>
        
        <!-- معلومات إضافية -->
        <div class="mt-8 bg-white shadow-lg rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">
                    <i class="fas fa-info-circle ml-2"></i>
                    معلومات مهمة حول نظام الأدوار المتقدم
                </h3>
            </div>
            
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">أنواع الصلاحيات</h4>
                        <ul class="space-y-2 text-sm text-gray-600">
                            <li><strong>صلاحيات الوحدات:</strong> التحكم في الوصول للوحدات الرئيسية</li>
                            <li><strong>صلاحيات العمليات:</strong> التحكم في العمليات المحددة</li>
                            <li><strong>صلاحيات الحقول:</strong> التحكم في الوصول لحقول معينة</li>
                            <li><strong>صلاحيات البيانات:</strong> التحكم في الوصول لبيانات محددة</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">مستويات الوصول</h4>
                        <ul class="space-y-2 text-sm text-gray-600">
                            <li><strong>قراءة:</strong> عرض البيانات فقط</li>
                            <li><strong>كتابة:</strong> إنشاء وتحديث البيانات</li>
                            <li><strong>حذف:</strong> حذف البيانات</li>
                            <li><strong>إدارة كاملة:</strong> جميع الصلاحيات</li>
                        </ul>
                    </div>
                    
                </div>
                
                <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h4 class="font-semibold text-blue-900 mb-2">الخطوات التالية</h4>
                    <ol class="list-decimal list-inside space-y-1 text-sm text-blue-800">
                        <li>إنشاء الأدوار الافتراضية</li>
                        <li>تخصيص الصلاحيات حسب الحاجة</li>
                        <li>تعيين الأدوار للمستخدمين</li>
                        <li>مراجعة وتحديث الصلاحيات دورياً</li>
                    </ol>
                </div>
            </div>
        </div>
        
    </div>

</body>
</html>
