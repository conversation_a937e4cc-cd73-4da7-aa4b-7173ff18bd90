<?php
/**
 * صفحة قائمة الدخل
 * Income Statement Page
 */

define('APP_INIT', true);
require_once 'includes/init.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('reports_view');

// إنشاء مثيل نموذج التقارير
$reportModel = new Report();

// معالجة المرشحات
$dateFrom = sanitizeInput($_GET['date_from'] ?? '');
$dateTo = sanitizeInput($_GET['date_to'] ?? '');

// تعيين تواريخ افتراضية إذا لم تكن محددة
if (empty($dateFrom)) {
    $dateFrom = date('Y-01-01'); // بداية السنة الحالية
}
if (empty($dateTo)) {
    $dateTo = date('Y-m-d'); // اليوم الحالي
}

// الحصول على قائمة الدخل
$incomeStatement = $reportModel->getIncomeStatement($dateFrom, $dateTo);

$pageTitle = 'قائمة الدخل';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- خط عربي -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- أيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        
        /* تحسين عرض التقرير */
        .report-table {
            font-size: 0.875rem;
        }
        
        .section-header {
            background-color: #f3f4f6;
            font-weight: 600;
        }
        
        .total-row {
            background-color: #1f2937;
            color: white;
            font-weight: 700;
        }
        
        .subtotal-row {
            background-color: #374151;
            color: white;
            font-weight: 600;
        }
        
        .net-income-positive {
            background-color: #065f46;
            color: white;
        }
        
        .net-income-negative {
            background-color: #991b1b;
            color: white;
        }
        
        @media print {
            .no-print { display: none; }
            body { font-size: 12px; }
            .report-table { font-size: 11px; }
        }
    </style>
</head>
<body class="bg-gray-50">

    <!-- شريط التنقل العلوي -->
    <nav class="gradient-bg shadow-lg no-print">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="dashboard.php" class="flex-shrink-0 flex items-center text-white hover:text-gray-200">
                        <i class="fas fa-arrow-right text-xl ml-3"></i>
                        <span>العودة للوحة التحكم</span>
                    </a>
                </div>
                
                <div class="flex items-center">
                    <h1 class="text-white text-xl font-bold">
                        <i class="fas fa-chart-line ml-2"></i>
                        <?php echo $pageTitle; ?>
                    </h1>
                </div>
                
                <div class="flex items-center space-x-4 space-x-reverse">
                    <span class="text-white text-sm">
                        <i class="fas fa-user ml-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </span>
                    <a href="logout.php" class="text-white hover:text-gray-200 transition-colors duration-200">
                        <i class="fas fa-sign-out-alt ml-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="max-w-5xl mx-auto py-6 sm:px-6 lg:px-8">
        
        <!-- عرض الرسائل -->
        <?php showAlert(); ?>
        
        <!-- قائمة الدخل -->
        <div class="bg-white shadow-lg rounded-lg">
            
            <!-- رأس التقرير -->
            <div class="px-6 py-4 border-b border-gray-200 no-print">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-chart-line ml-2"></i>
                        قائمة الدخل
                    </h3>
                    <div class="flex space-x-2 space-x-reverse">
                        <button onclick="window.print()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-print ml-2"></i>
                            طباعة
                        </button>
                        <a href="balance_sheet.php" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-balance-scale ml-2"></i>
                            الميزانية العمومية
                        </a>
                        <a href="reports.php" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-chart-bar ml-2"></i>
                            جميع التقارير
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- فلترة التواريخ -->
            <div class="px-6 py-4 bg-gray-50 border-b border-gray-200 no-print">
                <form method="GET" action="income_statement.php" class="flex items-center space-x-4 space-x-reverse">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">من تاريخ</label>
                        <input 
                            type="date" 
                            name="date_from" 
                            value="<?php echo htmlspecialchars($dateFrom); ?>"
                            class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">إلى تاريخ</label>
                        <input 
                            type="date" 
                            name="date_to" 
                            value="<?php echo htmlspecialchars($dateTo); ?>"
                            class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                    </div>
                    <div class="pt-6">
                        <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-search ml-2"></i>
                            عرض
                        </button>
                    </div>
                </form>
            </div>
            
            <!-- معلومات التقرير -->
            <div class="px-6 py-4 bg-blue-50 border-b border-gray-200">
                <div class="text-center">
                    <h2 class="text-xl font-bold text-gray-900 mb-2"><?php echo APP_NAME; ?></h2>
                    <h3 class="text-lg font-semibold text-gray-800 mb-1">قائمة الدخل</h3>
                    <p class="text-sm text-gray-600">
                        للفترة من <?php echo formatDate($dateFrom); ?> إلى <?php echo formatDate($dateTo); ?>
                    </p>
                    <p class="text-xs text-gray-500 mt-2">
                        تاريخ الطباعة: <?php echo formatDate(date('Y-m-d')); ?> - الوقت: <?php echo date('H:i'); ?>
                    </p>
                </div>
            </div>
            
            <!-- جدول قائمة الدخل -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 report-table">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">كود الحساب</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">اسم الحساب</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المبلغ</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        
                        <!-- الإيرادات -->
                        <tr class="section-header">
                            <td colspan="3" class="px-6 py-3 text-sm font-semibold text-gray-900">
                                الإيرادات
                            </td>
                        </tr>
                        
                        <?php if (empty($incomeStatement['revenues'])): ?>
                            <tr>
                                <td colspan="3" class="px-6 py-4 text-center text-gray-500 text-sm">
                                    لا توجد إيرادات في الفترة المحددة
                                </td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($incomeStatement['revenues'] as $revenue): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo htmlspecialchars($revenue['account_code']); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo htmlspecialchars($revenue['account_name']); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-left font-medium">
                                        <?php echo formatMoney($revenue['amount']); ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                        
                        <!-- إجمالي الإيرادات -->
                        <tr class="subtotal-row">
                            <td colspan="2" class="px-6 py-3 text-sm font-semibold">
                                إجمالي الإيرادات
                            </td>
                            <td class="px-6 py-3 text-sm font-bold text-left">
                                <?php echo formatMoney($incomeStatement['total_revenues']); ?>
                            </td>
                        </tr>
                        
                        <!-- المصروفات -->
                        <tr class="section-header">
                            <td colspan="3" class="px-6 py-3 text-sm font-semibold text-gray-900">
                                المصروفات
                            </td>
                        </tr>
                        
                        <?php if (empty($incomeStatement['expenses'])): ?>
                            <tr>
                                <td colspan="3" class="px-6 py-4 text-center text-gray-500 text-sm">
                                    لا توجد مصروفات في الفترة المحددة
                                </td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($incomeStatement['expenses'] as $expense): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo htmlspecialchars($expense['account_code']); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo htmlspecialchars($expense['account_name']); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-left font-medium">
                                        <?php echo formatMoney($expense['amount']); ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                        
                        <!-- إجمالي المصروفات -->
                        <tr class="subtotal-row">
                            <td colspan="2" class="px-6 py-3 text-sm font-semibold">
                                إجمالي المصروفات
                            </td>
                            <td class="px-6 py-3 text-sm font-bold text-left">
                                <?php echo formatMoney($incomeStatement['total_expenses']); ?>
                            </td>
                        </tr>
                        
                    </tbody>
                    
                    <!-- صافي الدخل -->
                    <tfoot>
                        <tr class="<?php echo $incomeStatement['net_income'] >= 0 ? 'net-income-positive' : 'net-income-negative'; ?>">
                            <td colspan="2" class="px-6 py-4 text-sm font-bold">
                                <?php echo $incomeStatement['net_income'] >= 0 ? 'صافي الربح' : 'صافي الخسارة'; ?>
                            </td>
                            <td class="px-6 py-4 text-sm font-bold text-left">
                                <?php echo formatMoney(abs($incomeStatement['net_income'])); ?>
                            </td>
                        </tr>
                    </tfoot>
                </table>
            </div>
            
            <!-- ملخص التقرير -->
            <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">
                            <?php echo formatMoney($incomeStatement['total_revenues']); ?>
                        </div>
                        <div class="text-sm text-gray-600">إجمالي الإيرادات</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-red-600">
                            <?php echo formatMoney($incomeStatement['total_expenses']); ?>
                        </div>
                        <div class="text-sm text-gray-600">إجمالي المصروفات</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold <?php echo $incomeStatement['net_income'] >= 0 ? 'text-green-600' : 'text-red-600'; ?>">
                            <?php echo formatMoney(abs($incomeStatement['net_income'])); ?>
                        </div>
                        <div class="text-sm text-gray-600">
                            <?php echo $incomeStatement['net_income'] >= 0 ? 'صافي الربح' : 'صافي الخسارة'; ?>
                        </div>
                    </div>
                </div>
                
                <?php if ($incomeStatement['net_income'] < 0): ?>
                    <div class="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-triangle text-red-500 ml-2"></i>
                            <span class="text-red-700 font-medium">تحذير: الشركة تحقق خسائر</span>
                        </div>
                        <p class="text-red-600 text-sm mt-1">
                            المصروفات تتجاوز الإيرادات بمبلغ <?php echo formatMoney(abs($incomeStatement['net_income'])); ?>
                        </p>
                    </div>
                <?php endif; ?>
            </div>
            
        </div>
        
    </div>

</body>
</html>
