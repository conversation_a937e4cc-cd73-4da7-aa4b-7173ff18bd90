<?php
/**
 * سكريبت تهيئة الإعدادات الافتراضية
 * Initialize Default Settings Script
 */

// تعيين المسار الأساسي
define('BASE_PATH', dirname(__DIR__) . '/');

// تضمين ملفات النظام
require_once BASE_PATH . 'includes/config.php';
require_once BASE_PATH . 'includes/database.php';
require_once BASE_PATH . 'includes/functions.php';
require_once BASE_PATH . 'models/Setting.php';

// التحقق من أن الملف يتم تشغيله من سطر الأوامر أو من المتصفح مع معاملات صحيحة
$isCommandLine = php_sapi_name() === 'cli';
$hasValidToken = isset($_GET['token']) && $_GET['token'] === 'init_settings_2024';

if (!$isCommandLine && !$hasValidToken) {
    die('غير مسموح بالوصول المباشر');
}

echo $isCommandLine ? "بدء تهيئة الإعدادات الافتراضية...\n" : "<h2>بدء تهيئة الإعدادات الافتراضية...</h2>";

try {
    // إنشاء مثيل نموذج الإعدادات
    $settingModel = new Setting();
    
    // تهيئة الإعدادات الافتراضية
    $result = $settingModel->initializeDefaultSettings();
    
    if ($result) {
        echo $isCommandLine ? "تم تهيئة الإعدادات الافتراضية بنجاح!\n" : "<p style='color: green;'>تم تهيئة الإعدادات الافتراضية بنجاح!</p>";
        
        // عرض الإعدادات المهيأة
        $allSettings = $settingModel->getAllSettings();
        
        echo $isCommandLine ? "\nالإعدادات المهيأة:\n" : "<h3>الإعدادات المهيأة:</h3><ul>";
        
        foreach ($allSettings as $key => $setting) {
            if ($isCommandLine) {
                echo "- {$key}: {$setting['setting_value']}\n";
            } else {
                echo "<li><strong>{$key}:</strong> {$setting['setting_value']}</li>";
            }
        }
        
        if (!$isCommandLine) {
            echo "</ul>";
        }
        
        echo $isCommandLine ? "\nتم الانتهاء من التهيئة.\n" : "<p>تم الانتهاء من التهيئة.</p>";
        
    } else {
        echo $isCommandLine ? "فشل في تهيئة الإعدادات!\n" : "<p style='color: red;'>فشل في تهيئة الإعدادات!</p>";
    }
    
} catch (Exception $e) {
    $errorMessage = "خطأ في تهيئة الإعدادات: " . $e->getMessage();
    echo $isCommandLine ? $errorMessage . "\n" : "<p style='color: red;'>{$errorMessage}</p>";
}

if (!$isCommandLine) {
    echo "<br><a href='../dashboard.php'>العودة للوحة التحكم</a>";
}

?>
