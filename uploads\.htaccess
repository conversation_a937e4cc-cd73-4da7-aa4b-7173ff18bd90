# حماية مجلد المرفقات
# Protect uploads directory

# منع الوصول المباشر للملفات التنفيذية
<Files "*.php">
    Order Deny,Allow
    Deny from all
</Files>

<Files "*.phtml">
    Order Deny,Allow
    Deny from all
</Files>

<Files "*.php3">
    Order Deny,Allow
    Deny from all
</Files>

<Files "*.php4">
    Order Deny,Allow
    Deny from all
</Files>

<Files "*.php5">
    Order Deny,Allow
    Deny from all
</Files>

<Files "*.pl">
    Order Deny,Allow
    Deny from all
</Files>

<Files "*.py">
    Order Deny,Allow
    Deny from all
</Files>

<Files "*.jsp">
    Order Deny,Allow
    Deny from all
</Files>

<Files "*.asp">
    Order Deny,Allow
    Deny from all
</Files>

<Files "*.sh">
    Order Deny,Allow
    Deny from all
</Files>

# السماح بأنواع الملفات الآمنة فقط
<FilesMatch "\.(jpg|jpeg|png|gif|pdf|doc|docx|xls|xlsx|txt|zip|rar|7z)$">
    Order Allow,Deny
    Allow from all
</FilesMatch>

# منع عرض محتويات المجلد
Options -Indexes

# إعدادات الأمان الإضافية
<IfModule mod_headers.c>
    Header set X-Content-Type-Options nosniff
    Header set X-Frame-Options DENY
    Header set X-XSS-Protection "1; mode=block"
</IfModule>
