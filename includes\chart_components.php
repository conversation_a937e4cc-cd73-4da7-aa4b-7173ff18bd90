<?php
/**
 * مكونات الرسوم البيانية بـ CSS
 * CSS Chart Components
 */

/**
 * رسم بياني دائري (Pie Chart)
 */
function renderPieChart($data, $title = '', $colors = null) {
    if (empty($data)) {
        return '<div class="chart-placeholder">لا توجد بيانات للعرض</div>';
    }
    
    // الألوان الافتراضية
    if (!$colors) {
        $colors = ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#06b6d4', '#84cc16', '#f97316'];
    }
    
    $total = array_sum($data);
    if ($total == 0) {
        return '<div class="chart-placeholder">لا توجد بيانات للعرض</div>';
    }
    
    $html = '<div class="pie-chart-container">';
    
    if ($title) {
        $html .= '<h4 class="chart-title">' . htmlspecialchars($title) . '</h4>';
    }
    
    $html .= '<div class="pie-chart">';
    
    $currentAngle = 0;
    $colorIndex = 0;
    
    foreach ($data as $label => $value) {
        $percentage = ($value / $total) * 100;
        $angle = ($value / $total) * 360;
        
        if ($percentage > 0) {
            $color = $colors[$colorIndex % count($colors)];
            
            // إنشاء قطعة الدائرة
            $html .= '<div class="pie-slice" style="
                --start-angle: ' . $currentAngle . 'deg;
                --end-angle: ' . ($currentAngle + $angle) . 'deg;
                --slice-color: ' . $color . ';
            "></div>';
            
            $currentAngle += $angle;
            $colorIndex++;
        }
    }
    
    $html .= '</div>';
    
    // إضافة وسيلة الإيضاح
    $html .= '<div class="chart-legend">';
    $colorIndex = 0;
    
    foreach ($data as $label => $value) {
        $percentage = ($value / $total) * 100;
        if ($percentage > 0) {
            $color = $colors[$colorIndex % count($colors)];
            $html .= '<div class="legend-item">
                <span class="legend-color" style="background-color: ' . $color . ';"></span>
                <span class="legend-label">' . htmlspecialchars($label) . '</span>
                <span class="legend-value">(' . number_format($percentage, 1) . '%)</span>
            </div>';
            $colorIndex++;
        }
    }
    
    $html .= '</div>';
    $html .= '</div>';
    
    return $html;
}

/**
 * رسم بياني عمودي (Bar Chart)
 */
function renderBarChart($data, $title = '', $color = '#3b82f6') {
    if (empty($data)) {
        return '<div class="chart-placeholder">لا توجد بيانات للعرض</div>';
    }
    
    $maxValue = max($data);
    if ($maxValue == 0) {
        return '<div class="chart-placeholder">لا توجد بيانات للعرض</div>';
    }
    
    $html = '<div class="bar-chart-container">';
    
    if ($title) {
        $html .= '<h4 class="chart-title">' . htmlspecialchars($title) . '</h4>';
    }
    
    $html .= '<div class="bar-chart">';
    
    foreach ($data as $label => $value) {
        $percentage = ($value / $maxValue) * 100;
        
        $html .= '<div class="bar-item">
            <div class="bar-label">' . htmlspecialchars($label) . '</div>
            <div class="bar-container">
                <div class="bar-fill" style="
                    width: ' . $percentage . '%;
                    background-color: ' . $color . ';
                "></div>
                <span class="bar-value">' . number_format($value, 0) . '</span>
            </div>
        </div>';
    }
    
    $html .= '</div>';
    $html .= '</div>';
    
    return $html;
}

/**
 * رسم بياني خطي (Line Chart)
 */
function renderLineChart($data, $title = '', $color = '#3b82f6') {
    if (empty($data)) {
        return '<div class="chart-placeholder">لا توجد بيانات للعرض</div>';
    }
    
    $values = array_values($data);
    $maxValue = max($values);
    $minValue = min($values);
    
    if ($maxValue == $minValue) {
        return '<div class="chart-placeholder">لا توجد تباين في البيانات</div>';
    }
    
    $html = '<div class="line-chart-container">';
    
    if ($title) {
        $html .= '<h4 class="chart-title">' . htmlspecialchars($title) . '</h4>';
    }
    
    $html .= '<div class="line-chart">';
    
    // إنشاء نقاط الخط
    $points = [];
    $width = 100; // عرض الرسم بالنسبة المئوية
    $stepX = $width / (count($data) - 1);
    $currentX = 0;
    
    foreach ($values as $value) {
        $y = (($value - $minValue) / ($maxValue - $minValue)) * 80 + 10; // 10% هامش علوي وسفلي
        $points[] = $currentX . ',' . (100 - $y); // عكس Y للعرض الصحيح
        $currentX += $stepX;
    }
    
    $pathData = 'M ' . implode(' L ', $points);
    
    $html .= '<svg class="line-svg" viewBox="0 0 100 100" preserveAspectRatio="none">
        <path d="' . $pathData . '" stroke="' . $color . '" stroke-width="2" fill="none"/>
    </svg>';
    
    // إضافة التسميات
    $html .= '<div class="line-labels">';
    foreach ($data as $label => $value) {
        $html .= '<span class="line-label">' . htmlspecialchars($label) . '</span>';
    }
    $html .= '</div>';
    
    $html .= '</div>';
    $html .= '</div>';
    
    return $html;
}

/**
 * مؤشر دائري (Progress Circle)
 */
function renderProgressCircle($percentage, $title = '', $color = '#3b82f6') {
    $percentage = max(0, min(100, $percentage)); // تحديد النسبة بين 0 و 100
    
    $html = '<div class="progress-circle-container">';
    
    if ($title) {
        $html .= '<h4 class="chart-title">' . htmlspecialchars($title) . '</h4>';
    }
    
    $html .= '<div class="progress-circle" style="--progress: ' . $percentage . '%; --color: ' . $color . ';">
        <div class="progress-inner">
            <span class="progress-text">' . number_format($percentage, 1) . '%</span>
        </div>
    </div>';
    
    $html .= '</div>';
    
    return $html;
}

/**
 * شريط تقدم أفقي (Progress Bar)
 */
function renderProgressBar($percentage, $title = '', $color = '#3b82f6') {
    $percentage = max(0, min(100, $percentage));
    
    $html = '<div class="progress-bar-container">';
    
    if ($title) {
        $html .= '<h4 class="chart-title">' . htmlspecialchars($title) . '</h4>';
    }
    
    $html .= '<div class="progress-bar">
        <div class="progress-fill" style="width: ' . $percentage . '%; background-color: ' . $color . ';"></div>
        <span class="progress-label">' . number_format($percentage, 1) . '%</span>
    </div>';
    
    $html .= '</div>';
    
    return $html;
}

/**
 * مقياس (Gauge)
 */
function renderGauge($value, $max, $title = '', $color = '#3b82f6') {
    $percentage = ($value / $max) * 100;
    $angle = ($percentage / 100) * 180; // نصف دائرة
    
    $html = '<div class="gauge-container">';
    
    if ($title) {
        $html .= '<h4 class="chart-title">' . htmlspecialchars($title) . '</h4>';
    }
    
    $html .= '<div class="gauge">
        <div class="gauge-fill" style="
            --angle: ' . $angle . 'deg;
            --color: ' . $color . ';
        "></div>
        <div class="gauge-center">
            <span class="gauge-value">' . number_format($value, 0) . '</span>
            <span class="gauge-max">/ ' . number_format($max, 0) . '</span>
        </div>
    </div>';
    
    $html .= '</div>';
    
    return $html;
}

/**
 * جدول إحصائيات
 */
function renderStatsTable($data, $title = '', $headers = null) {
    if (empty($data)) {
        return '<div class="chart-placeholder">لا توجد بيانات للعرض</div>';
    }
    
    $html = '<div class="stats-table-container">';
    
    if ($title) {
        $html .= '<h4 class="chart-title">' . htmlspecialchars($title) . '</h4>';
    }
    
    $html .= '<div class="stats-table">';
    
    // رأس الجدول
    if ($headers) {
        $html .= '<div class="stats-row stats-header">';
        foreach ($headers as $header) {
            $html .= '<div class="stats-cell">' . htmlspecialchars($header) . '</div>';
        }
        $html .= '</div>';
    }
    
    // صفوف البيانات
    foreach ($data as $row) {
        $html .= '<div class="stats-row">';
        if (is_array($row)) {
            foreach ($row as $cell) {
                $html .= '<div class="stats-cell">' . htmlspecialchars($cell) . '</div>';
            }
        } else {
            $html .= '<div class="stats-cell">' . htmlspecialchars($row) . '</div>';
        }
        $html .= '</div>';
    }
    
    $html .= '</div>';
    $html .= '</div>';
    
    return $html;
}

/**
 * بطاقة إحصائية
 */
function renderStatCard($title, $value, $subtitle = '', $icon = '', $color = '#3b82f6', $trend = null) {
    $html = '<div class="stat-card" style="border-left-color: ' . $color . ';">';
    
    $html .= '<div class="stat-header">';
    if ($icon) {
        $html .= '<i class="' . $icon . ' stat-icon" style="color: ' . $color . ';"></i>';
    }
    $html .= '<h4 class="stat-title">' . htmlspecialchars($title) . '</h4>';
    $html .= '</div>';
    
    $html .= '<div class="stat-value">' . htmlspecialchars($value) . '</div>';
    
    if ($subtitle) {
        $html .= '<div class="stat-subtitle">' . htmlspecialchars($subtitle) . '</div>';
    }
    
    if ($trend !== null) {
        $trendClass = $trend > 0 ? 'trend-up' : ($trend < 0 ? 'trend-down' : 'trend-neutral');
        $trendIcon = $trend > 0 ? 'fa-arrow-up' : ($trend < 0 ? 'fa-arrow-down' : 'fa-minus');
        $html .= '<div class="stat-trend ' . $trendClass . '">
            <i class="fas ' . $trendIcon . '"></i>
            <span>' . abs($trend) . '%</span>
        </div>';
    }
    
    $html .= '</div>';
    
    return $html;
}

?>
