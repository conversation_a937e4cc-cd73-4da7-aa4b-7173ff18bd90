<?php
/**
 * مجدول النسخ الاحتياطي التلقائي
 * Automatic Backup Scheduler
 * 
 * يتم تشغيل هذا الملف عبر Cron Job لإنشاء نسخ احتياطية تلقائية
 * This file is executed via Cron Job to create automatic backups
 */

// تعيين المسار الأساسي
define('BASE_PATH', dirname(__DIR__) . '/');

// تضمين ملفات النظام
require_once BASE_PATH . 'includes/config.php';
require_once BASE_PATH . 'includes/database.php';
require_once BASE_PATH . 'includes/functions.php';
require_once BASE_PATH . 'models/Backup.php';
require_once BASE_PATH . 'models/ActivityLog.php';

// التحقق من أن الملف يتم تشغيله من سطر الأوامر
if (php_sapi_name() !== 'cli') {
    die('هذا الملف يجب تشغيله من سطر الأوامر فقط');
}

/**
 * فئة مجدول النسخ الاحتياطي
 */
class BackupScheduler {
    private $backupModel;
    private $activityLogModel;
    private $config;
    
    public function __construct() {
        $this->backupModel = new Backup();
        $this->activityLogModel = new ActivityLog();
        
        // إعدادات الجدولة
        $this->config = [
            'daily_backup' => true,           // نسخة احتياطية يومية
            'weekly_full_backup' => true,     // نسخة كاملة أسبوعية
            'monthly_cleanup' => true,        // تنظيف شهري
            'max_daily_backups' => 7,         // الاحتفاظ بـ 7 نسخ يومية
            'max_weekly_backups' => 4,        // الاحتفاظ بـ 4 نسخ أسبوعية
            'max_monthly_backups' => 12,      // الاحتفاظ بـ 12 نسخة شهرية
            'backup_time' => '02:00',          // وقت النسخ الاحتياطي (2:00 صباحاً)
            'cleanup_days' => 30              // حذف النسخ الأقدم من 30 يوم
        ];
    }
    
    /**
     * تشغيل المجدول
     */
    public function run() {
        $this->log("بدء تشغيل مجدول النسخ الاحتياطي");
        
        try {
            $currentHour = date('H:i');
            $dayOfWeek = date('w'); // 0 = الأحد، 1 = الاثنين، إلخ
            $dayOfMonth = date('j');
            
            // النسخ الاحتياطي اليومي
            if ($this->config['daily_backup'] && $currentHour === $this->config['backup_time']) {
                $this->createDailyBackup();
            }
            
            // النسخ الاحتياطي الأسبوعي (يوم الأحد)
            if ($this->config['weekly_full_backup'] && $dayOfWeek == 0 && $currentHour === $this->config['backup_time']) {
                $this->createWeeklyBackup();
            }
            
            // التنظيف الشهري (اليوم الأول من كل شهر)
            if ($this->config['monthly_cleanup'] && $dayOfMonth == 1 && $currentHour === $this->config['backup_time']) {
                $this->performMonthlyCleanup();
            }
            
            // تنظيف النسخ القديمة (يومياً)
            $this->cleanupOldBackups();
            
            $this->log("انتهاء تشغيل مجدول النسخ الاحتياطي بنجاح");
            
        } catch (Exception $e) {
            $this->log("خطأ في مجدول النسخ الاحتياطي: " . $e->getMessage(), 'error');
        }
    }
    
    /**
     * إنشاء نسخة احتياطية يومية
     */
    private function createDailyBackup() {
        $this->log("بدء إنشاء النسخة الاحتياطية اليومية");
        
        $description = "نسخة احتياطية يومية تلقائية - " . date('Y-m-d');
        $result = $this->backupModel->createDatabaseBackup($description);
        
        if ($result['success']) {
            $this->log("تم إنشاء النسخة الاحتياطية اليومية بنجاح: " . $result['filename']);
            
            // حذف النسخ اليومية الزائدة
            $this->cleanupBackupsByType('database', $this->config['max_daily_backups']);
        } else {
            $this->log("فشل في إنشاء النسخة الاحتياطية اليومية: " . $result['error'], 'error');
        }
    }
    
    /**
     * إنشاء نسخة احتياطية أسبوعية كاملة
     */
    private function createWeeklyBackup() {
        $this->log("بدء إنشاء النسخة الاحتياطية الأسبوعية");
        
        $description = "نسخة احتياطية أسبوعية كاملة تلقائية - أسبوع " . date('W') . " من " . date('Y');
        $result = $this->backupModel->createFullBackup($description);
        
        if ($result['success']) {
            $this->log("تم إنشاء النسخة الاحتياطية الأسبوعية بنجاح: " . $result['filename']);
            
            // حذف النسخ الأسبوعية الزائدة
            $this->cleanupBackupsByType('full', $this->config['max_weekly_backups']);
        } else {
            $this->log("فشل في إنشاء النسخة الاحتياطية الأسبوعية: " . $result['error'], 'error');
        }
    }
    
    /**
     * التنظيف الشهري
     */
    private function performMonthlyCleanup() {
        $this->log("بدء التنظيف الشهري");
        
        // تنظيف النسخ القديمة
        $deletedCount = $this->backupModel->cleanOldBackups($this->config['cleanup_days']);
        
        if ($deletedCount !== false) {
            $this->log("تم حذف {$deletedCount} نسخة احتياطية قديمة في التنظيف الشهري");
        } else {
            $this->log("فشل في التنظيف الشهري", 'error');
        }
        
        // تنظيف سجل الأنشطة القديم
        $activityDeletedCount = $this->activityLogModel->cleanOldLogs(90);
        
        if ($activityDeletedCount !== false) {
            $this->log("تم حذف {$activityDeletedCount} سجل نشاط قديم في التنظيف الشهري");
        }
    }
    
    /**
     * تنظيف النسخ القديمة حسب النوع
     */
    private function cleanupBackupsByType($backupType, $maxCount) {
        try {
            $db = Database::getInstance();
            
            // الحصول على النسخ الاحتياطية من النوع المحدد مرتبة حسب التاريخ
            $query = "SELECT backup_id, filename, filepath 
                     FROM backups 
                     WHERE backup_type = ? AND status = 'completed'
                     ORDER BY created_at DESC";
            
            $backups = $db->select($query, [$backupType]);
            
            // حذف النسخ الزائدة
            if (count($backups) > $maxCount) {
                $backupsToDelete = array_slice($backups, $maxCount);
                
                foreach ($backupsToDelete as $backup) {
                    if ($this->backupModel->deleteBackup($backup['backup_id'])) {
                        $this->log("تم حذف النسخة الاحتياطية الزائدة: " . $backup['filename']);
                    }
                }
            }
            
        } catch (Exception $e) {
            $this->log("خطأ في تنظيف النسخ حسب النوع: " . $e->getMessage(), 'error');
        }
    }
    
    /**
     * تنظيف النسخ القديمة
     */
    private function cleanupOldBackups() {
        // تنظيف النسخ الأقدم من المدة المحددة
        $deletedCount = $this->backupModel->cleanOldBackups($this->config['cleanup_days']);
        
        if ($deletedCount > 0) {
            $this->log("تم حذف {$deletedCount} نسخة احتياطية قديمة");
        }
    }
    
    /**
     * تسجيل الأحداث
     */
    private function log($message, $level = 'info') {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;
        
        // كتابة في ملف السجل
        $logFile = BASE_PATH . 'logs/backup_scheduler.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
        
        // طباعة في وحدة التحكم إذا كان يتم التشغيل من سطر الأوامر
        if (php_sapi_name() === 'cli') {
            echo $logMessage;
        }
        
        // تسجيل في قاعدة البيانات للأحداث المهمة
        if ($level === 'error' || strpos($message, 'تم إنشاء') !== false) {
            try {
                $this->activityLogModel->logActivity(
                    null, // مستخدم النظام
                    'مجدول النسخ الاحتياطي',
                    $message
                );
            } catch (Exception $e) {
                // تجاهل أخطاء تسجيل الأنشطة لتجنب التكرار اللانهائي
            }
        }
    }
    
    /**
     * فحص حالة النظام
     */
    public function checkSystemHealth() {
        $this->log("بدء فحص حالة النظام");
        
        $issues = [];
        
        // فحص مساحة القرص
        $backupDir = BASE_PATH . 'backups/';
        $freeSpace = disk_free_space($backupDir);
        $totalSpace = disk_total_space($backupDir);
        $usedPercentage = (($totalSpace - $freeSpace) / $totalSpace) * 100;
        
        if ($usedPercentage > 90) {
            $issues[] = "مساحة القرص ممتلئة تقريباً ({$usedPercentage}%)";
        }
        
        // فحص آخر نسخة احتياطية
        $db = Database::getInstance();
        $lastBackup = $db->selectOne(
            "SELECT created_at FROM backups WHERE status = 'completed' ORDER BY created_at DESC LIMIT 1"
        );
        
        if ($lastBackup) {
            $lastBackupTime = strtotime($lastBackup['created_at']);
            $hoursSinceLastBackup = (time() - $lastBackupTime) / 3600;
            
            if ($hoursSinceLastBackup > 48) { // أكثر من 48 ساعة
                $issues[] = "لم يتم إنشاء نسخة احتياطية منذ أكثر من 48 ساعة";
            }
        } else {
            $issues[] = "لا توجد نسخ احتياطية ناجحة";
        }
        
        // تسجيل المشاكل
        if (!empty($issues)) {
            foreach ($issues as $issue) {
                $this->log("مشكلة في النظام: " . $issue, 'error');
            }
        } else {
            $this->log("فحص حالة النظام: كل شيء يعمل بشكل طبيعي");
        }
        
        return $issues;
    }
}

// تشغيل المجدول
if (php_sapi_name() === 'cli') {
    $scheduler = new BackupScheduler();
    
    // فحص المعاملات
    $action = $argv[1] ?? 'run';
    
    switch ($action) {
        case 'run':
            $scheduler->run();
            break;
            
        case 'health':
            $issues = $scheduler->checkSystemHealth();
            if (empty($issues)) {
                echo "النظام يعمل بشكل طبيعي\n";
                exit(0);
            } else {
                echo "تم العثور على مشاكل:\n";
                foreach ($issues as $issue) {
                    echo "- {$issue}\n";
                }
                exit(1);
            }
            break;
            
        case 'daily':
            $scheduler->createDailyBackup();
            break;
            
        case 'weekly':
            $scheduler->createWeeklyBackup();
            break;
            
        case 'cleanup':
            $scheduler->performMonthlyCleanup();
            break;
            
        default:
            echo "الاستخدام: php backup_scheduler.php [run|health|daily|weekly|cleanup]\n";
            exit(1);
    }
}

?>
