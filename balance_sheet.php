<?php
/**
 * صفحة الميزانية العمومية
 * Balance Sheet Page
 */

define('APP_INIT', true);
require_once 'includes/init.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('reports_view');

// إنشاء مثيل نموذج التقارير
$reportModel = new Report();

// معالجة المرشحات
$asOfDate = sanitizeInput($_GET['as_of_date'] ?? '');

// تعيين تاريخ افتراضي إذا لم يكن محدداً
if (empty($asOfDate)) {
    $asOfDate = date('Y-m-d'); // اليوم الحالي
}

// الحصول على الميزانية العمومية
$balanceSheet = $reportModel->getBalanceSheet($asOfDate);

$pageTitle = 'الميزانية العمومية';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- خط عربي -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- أيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        
        /* تحسين عرض التقرير */
        .report-table {
            font-size: 0.875rem;
        }
        
        .section-header {
            background-color: #f3f4f6;
            font-weight: 600;
        }
        
        .total-row {
            background-color: #1f2937;
            color: white;
            font-weight: 700;
        }
        
        .subtotal-row {
            background-color: #374151;
            color: white;
            font-weight: 600;
        }
        
        .balanced-sheet {
            background-color: #065f46;
            color: white;
        }
        
        .unbalanced-sheet {
            background-color: #991b1b;
            color: white;
        }
        
        @media print {
            .no-print { display: none; }
            body { font-size: 12px; }
            .report-table { font-size: 11px; }
        }
    </style>
</head>
<body class="bg-gray-50">

    <!-- شريط التنقل العلوي -->
    <nav class="gradient-bg shadow-lg no-print">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="dashboard.php" class="flex-shrink-0 flex items-center text-white hover:text-gray-200">
                        <i class="fas fa-arrow-right text-xl ml-3"></i>
                        <span>العودة للوحة التحكم</span>
                    </a>
                </div>
                
                <div class="flex items-center">
                    <h1 class="text-white text-xl font-bold">
                        <i class="fas fa-balance-scale ml-2"></i>
                        <?php echo $pageTitle; ?>
                    </h1>
                </div>
                
                <div class="flex items-center space-x-4 space-x-reverse">
                    <span class="text-white text-sm">
                        <i class="fas fa-user ml-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </span>
                    <a href="logout.php" class="text-white hover:text-gray-200 transition-colors duration-200">
                        <i class="fas fa-sign-out-alt ml-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="max-w-6xl mx-auto py-6 sm:px-6 lg:px-8">
        
        <!-- عرض الرسائل -->
        <?php showAlert(); ?>
        
        <!-- الميزانية العمومية -->
        <div class="bg-white shadow-lg rounded-lg">
            
            <!-- رأس التقرير -->
            <div class="px-6 py-4 border-b border-gray-200 no-print">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-balance-scale ml-2"></i>
                        الميزانية العمومية
                    </h3>
                    <div class="flex space-x-2 space-x-reverse">
                        <button onclick="window.print()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-print ml-2"></i>
                            طباعة
                        </button>
                        <a href="income_statement.php" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-chart-line ml-2"></i>
                            قائمة الدخل
                        </a>
                        <a href="reports.php" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-chart-bar ml-2"></i>
                            جميع التقارير
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- فلترة التاريخ -->
            <div class="px-6 py-4 bg-gray-50 border-b border-gray-200 no-print">
                <form method="GET" action="balance_sheet.php" class="flex items-center space-x-4 space-x-reverse">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">كما في تاريخ</label>
                        <input 
                            type="date" 
                            name="as_of_date" 
                            value="<?php echo htmlspecialchars($asOfDate); ?>"
                            class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                    </div>
                    <div class="pt-6">
                        <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-search ml-2"></i>
                            عرض
                        </button>
                    </div>
                </form>
            </div>
            
            <!-- معلومات التقرير -->
            <div class="px-6 py-4 bg-blue-50 border-b border-gray-200">
                <div class="text-center">
                    <h2 class="text-xl font-bold text-gray-900 mb-2"><?php echo APP_NAME; ?></h2>
                    <h3 class="text-lg font-semibold text-gray-800 mb-1">الميزانية العمومية</h3>
                    <p class="text-sm text-gray-600">
                        كما في <?php echo formatDate($asOfDate); ?>
                    </p>
                    <p class="text-xs text-gray-500 mt-2">
                        تاريخ الطباعة: <?php echo formatDate(date('Y-m-d')); ?> - الوقت: <?php echo date('H:i'); ?>
                    </p>
                </div>
            </div>
            
            <!-- جدول الميزانية العمومية -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-0">
                
                <!-- الأصول -->
                <div class="border-l border-gray-200">
                    <table class="min-w-full divide-y divide-gray-200 report-table">
                        <thead class="bg-gray-50">
                            <tr>
                                <th colspan="2" class="px-6 py-3 text-center text-sm font-medium text-gray-900 uppercase tracking-wider">
                                    الأصول
                                </th>
                            </tr>
                            <tr class="bg-gray-100">
                                <th class="px-6 py-2 text-right text-xs font-medium text-gray-500 uppercase">اسم الحساب</th>
                                <th class="px-6 py-2 text-right text-xs font-medium text-gray-500 uppercase">المبلغ</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php if (empty($balanceSheet['assets'])): ?>
                                <tr>
                                    <td colspan="2" class="px-6 py-8 text-center text-gray-500 text-sm">
                                        لا توجد أصول
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($balanceSheet['assets'] as $asset): ?>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-3 text-sm text-gray-900">
                                            <div class="font-medium"><?php echo htmlspecialchars($asset['account_name']); ?></div>
                                            <div class="text-xs text-gray-500"><?php echo htmlspecialchars($asset['account_code']); ?></div>
                                        </td>
                                        <td class="px-6 py-3 text-sm text-gray-900 text-left font-medium">
                                            <?php echo formatMoney($asset['amount']); ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                        <tfoot>
                            <tr class="subtotal-row">
                                <td class="px-6 py-3 text-sm font-bold">
                                    إجمالي الأصول
                                </td>
                                <td class="px-6 py-3 text-sm font-bold text-left">
                                    <?php echo formatMoney($balanceSheet['total_assets']); ?>
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                
                <!-- الخصوم وحقوق الملكية -->
                <div>
                    <table class="min-w-full divide-y divide-gray-200 report-table">
                        <thead class="bg-gray-50">
                            <tr>
                                <th colspan="2" class="px-6 py-3 text-center text-sm font-medium text-gray-900 uppercase tracking-wider">
                                    الخصوم وحقوق الملكية
                                </th>
                            </tr>
                            <tr class="bg-gray-100">
                                <th class="px-6 py-2 text-right text-xs font-medium text-gray-500 uppercase">اسم الحساب</th>
                                <th class="px-6 py-2 text-right text-xs font-medium text-gray-500 uppercase">المبلغ</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            
                            <!-- الخصوم -->
                            <tr class="section-header">
                                <td colspan="2" class="px-6 py-2 text-sm font-semibold text-gray-900">
                                    الخصوم
                                </td>
                            </tr>
                            
                            <?php if (empty($balanceSheet['liabilities'])): ?>
                                <tr>
                                    <td colspan="2" class="px-6 py-3 text-center text-gray-500 text-sm">
                                        لا توجد خصوم
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($balanceSheet['liabilities'] as $liability): ?>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-3 text-sm text-gray-900">
                                            <div class="font-medium"><?php echo htmlspecialchars($liability['account_name']); ?></div>
                                            <div class="text-xs text-gray-500"><?php echo htmlspecialchars($liability['account_code']); ?></div>
                                        </td>
                                        <td class="px-6 py-3 text-sm text-gray-900 text-left font-medium">
                                            <?php echo formatMoney($liability['amount']); ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                            
                            <!-- إجمالي الخصوم -->
                            <tr class="bg-gray-100">
                                <td class="px-6 py-2 text-sm font-semibold text-gray-700">
                                    إجمالي الخصوم
                                </td>
                                <td class="px-6 py-2 text-sm font-semibold text-gray-700 text-left">
                                    <?php echo formatMoney($balanceSheet['total_liabilities']); ?>
                                </td>
                            </tr>
                            
                            <!-- حقوق الملكية -->
                            <tr class="section-header">
                                <td colspan="2" class="px-6 py-2 text-sm font-semibold text-gray-900">
                                    حقوق الملكية
                                </td>
                            </tr>
                            
                            <?php if (empty($balanceSheet['equity'])): ?>
                                <tr>
                                    <td colspan="2" class="px-6 py-3 text-center text-gray-500 text-sm">
                                        لا توجد حقوق ملكية
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($balanceSheet['equity'] as $equityItem): ?>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-3 text-sm text-gray-900">
                                            <div class="font-medium"><?php echo htmlspecialchars($equityItem['account_name']); ?></div>
                                            <div class="text-xs text-gray-500"><?php echo htmlspecialchars($equityItem['account_code']); ?></div>
                                        </td>
                                        <td class="px-6 py-3 text-sm text-gray-900 text-left font-medium">
                                            <?php echo formatMoney($equityItem['amount']); ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                            
                            <!-- إجمالي حقوق الملكية -->
                            <tr class="bg-gray-100">
                                <td class="px-6 py-2 text-sm font-semibold text-gray-700">
                                    إجمالي حقوق الملكية
                                </td>
                                <td class="px-6 py-2 text-sm font-semibold text-gray-700 text-left">
                                    <?php echo formatMoney($balanceSheet['total_equity']); ?>
                                </td>
                            </tr>
                            
                        </tbody>
                        <tfoot>
                            <tr class="subtotal-row">
                                <td class="px-6 py-3 text-sm font-bold">
                                    إجمالي الخصوم وحقوق الملكية
                                </td>
                                <td class="px-6 py-3 text-sm font-bold text-left">
                                    <?php echo formatMoney($balanceSheet['total_liabilities'] + $balanceSheet['total_equity']); ?>
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                
            </div>
            
            <!-- التحقق من التوازن -->
            <div class="border-t border-gray-200">
                <?php 
                $totalLiabilitiesEquity = $balanceSheet['total_liabilities'] + $balanceSheet['total_equity'];
                $isBalanced = abs($balanceSheet['total_assets'] - $totalLiabilitiesEquity) < 0.01;
                ?>
                <div class="<?php echo $isBalanced ? 'balanced-sheet' : 'unbalanced-sheet'; ?> px-6 py-4">
                    <div class="text-center">
                        <div class="text-lg font-bold">
                            <?php echo $isBalanced ? 'الميزانية متوازنة' : 'الميزانية غير متوازنة'; ?>
                            <i class="fas fa-<?php echo $isBalanced ? 'check-circle' : 'exclamation-triangle'; ?> mr-2"></i>
                        </div>
                        <?php if (!$isBalanced): ?>
                            <div class="text-sm mt-1">
                                الفرق: <?php echo formatMoney(abs($balanceSheet['total_assets'] - $totalLiabilitiesEquity)); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- ملخص التقرير -->
            <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">
                            <?php echo formatMoney($balanceSheet['total_assets']); ?>
                        </div>
                        <div class="text-sm text-gray-600">إجمالي الأصول</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-red-600">
                            <?php echo formatMoney($balanceSheet['total_liabilities']); ?>
                        </div>
                        <div class="text-sm text-gray-600">إجمالي الخصوم</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">
                            <?php echo formatMoney($balanceSheet['total_equity']); ?>
                        </div>
                        <div class="text-sm text-gray-600">إجمالي حقوق الملكية</div>
                    </div>
                </div>
            </div>
            
        </div>
        
    </div>

</body>
</html>
