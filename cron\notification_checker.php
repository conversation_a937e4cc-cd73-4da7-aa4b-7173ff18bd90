<?php
/**
 * مجدول فحص الإشعارات التلقائي
 * Automatic Notification Checker
 * 
 * يتم تشغيل هذا الملف عبر Cron Job لفحص وإنشاء الإشعارات التلقائية
 * This file is executed via Cron Job to check and create automatic notifications
 */

// تعيين المسار الأساسي
define('BASE_PATH', dirname(__DIR__) . '/');

// تضمين ملفات النظام
require_once BASE_PATH . 'includes/config.php';
require_once BASE_PATH . 'includes/database.php';
require_once BASE_PATH . 'includes/functions.php';
require_once BASE_PATH . 'models/Notification.php';
require_once BASE_PATH . 'models/ActivityLog.php';

// التحقق من أن الملف يتم تشغيله من سطر الأوامر
if (php_sapi_name() !== 'cli') {
    die('هذا الملف يجب تشغيله من سطر الأوامر فقط');
}

/**
 * فئة مجدول فحص الإشعارات
 */
class NotificationChecker {
    private $notificationModel;
    private $activityLogModel;
    
    public function __construct() {
        $this->notificationModel = new Notification();
        $this->activityLogModel = new ActivityLog();
    }
    
    /**
     * تشغيل المجدول
     */
    public function run() {
        $this->log("بدء تشغيل مجدول فحص الإشعارات");
        
        try {
            // تشغيل جميع فحوصات الإشعارات
            $results = $this->notificationModel->runAllChecks();
            
            $totalNewNotifications = 0;
            foreach ($results as $checkType => $count) {
                if ($count !== false && $count > 0) {
                    $totalNewNotifications += $count;
                    $this->log("فحص {$checkType}: تم إنشاء {$count} إشعار جديد");
                }
            }
            
            if ($totalNewNotifications > 0) {
                $this->log("تم إنشاء {$totalNewNotifications} إشعار جديد إجمالي");
            } else {
                $this->log("لا توجد إشعارات جديدة");
            }
            
            // تنظيف الإشعارات القديمة
            $this->cleanupOldNotifications();
            
            $this->log("انتهاء تشغيل مجدول فحص الإشعارات بنجاح");
            
        } catch (Exception $e) {
            $this->log("خطأ في مجدول فحص الإشعارات: " . $e->getMessage(), 'error');
        }
    }
    
    /**
     * فحص المخزون المنخفض فقط
     */
    public function checkLowStock() {
        $this->log("بدء فحص المخزون المنخفض");
        
        $count = $this->notificationModel->checkLowStockNotifications();
        
        if ($count !== false) {
            if ($count > 0) {
                $this->log("تم إنشاء {$count} إشعار للمخزون المنخفض");
            } else {
                $this->log("لا توجد أصناف بمخزون منخفض");
            }
        } else {
            $this->log("فشل في فحص المخزون المنخفض", 'error');
        }
        
        return $count;
    }
    
    /**
     * فحص الفواتير المستحقة فقط
     */
    public function checkOverdueInvoices() {
        $this->log("بدء فحص الفواتير المستحقة");
        
        $count = $this->notificationModel->checkOverdueInvoicesNotifications();
        
        if ($count !== false) {
            if ($count > 0) {
                $this->log("تم إنشاء {$count} إشعار للفواتير المستحقة");
            } else {
                $this->log("لا توجد فواتير مستحقة");
            }
        } else {
            $this->log("فشل في فحص الفواتير المستحقة", 'error');
        }
        
        return $count;
    }
    
    /**
     * فحص النسخ الاحتياطية فقط
     */
    public function checkBackups() {
        $this->log("بدء فحص النسخ الاحتياطية");
        
        $count = $this->notificationModel->checkBackupNotifications();
        
        if ($count !== false) {
            if ($count > 0) {
                $this->log("تم إنشاء {$count} إشعار للنسخ الاحتياطية");
            } else {
                $this->log("النسخ الاحتياطية في حالة جيدة");
            }
        } else {
            $this->log("فشل في فحص النسخ الاحتياطية", 'error');
        }
        
        return $count;
    }
    
    /**
     * تنظيف الإشعارات القديمة
     */
    private function cleanupOldNotifications() {
        $this->log("بدء تنظيف الإشعارات القديمة");
        
        $deletedCount = $this->notificationModel->cleanOldNotifications(30);
        
        if ($deletedCount !== false) {
            if ($deletedCount > 0) {
                $this->log("تم حذف {$deletedCount} إشعار قديم");
            } else {
                $this->log("لا توجد إشعارات قديمة للحذف");
            }
        } else {
            $this->log("فشل في تنظيف الإشعارات القديمة", 'error');
        }
    }
    
    /**
     * إنشاء إشعار تجريبي
     */
    public function createTestNotification() {
        $this->log("إنشاء إشعار تجريبي");
        
        $notificationId = $this->notificationModel->createNotification([
            'notification_type' => 'system',
            'title' => 'إشعار تجريبي',
            'message' => 'هذا إشعار تجريبي تم إنشاؤه في ' . date('Y-m-d H:i:s'),
            'priority' => 'low'
        ]);
        
        if ($notificationId) {
            $this->log("تم إنشاء إشعار تجريبي بنجاح (ID: {$notificationId})");
        } else {
            $this->log("فشل في إنشاء الإشعار التجريبي", 'error');
        }
        
        return $notificationId;
    }
    
    /**
     * الحصول على إحصائيات الإشعارات
     */
    public function getStats() {
        $this->log("الحصول على إحصائيات الإشعارات");
        
        $stats = $this->notificationModel->getNotificationStats();
        
        $this->log("إحصائيات الإشعارات:");
        $this->log("- إجمالي الإشعارات: " . $stats['general']['total_notifications']);
        $this->log("- غير المقروءة: " . $stats['general']['unread_notifications']);
        $this->log("- عالية الأولوية: " . $stats['general']['high_priority_unread']);
        $this->log("- إشعارات اليوم: " . $stats['general']['today_notifications']);
        
        if (!empty($stats['by_type'])) {
            $this->log("إحصائيات حسب النوع:");
            foreach ($stats['by_type'] as $type) {
                $this->log("- {$type['notification_type']}: {$type['count']} (غير مقروءة: {$type['unread_count']})");
            }
        }
        
        return $stats;
    }
    
    /**
     * تسجيل الأحداث
     */
    private function log($message, $level = 'info') {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;
        
        // كتابة في ملف السجل
        $logFile = BASE_PATH . 'logs/notification_checker.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
        
        // طباعة في وحدة التحكم إذا كان يتم التشغيل من سطر الأوامر
        if (php_sapi_name() === 'cli') {
            echo $logMessage;
        }
        
        // تسجيل في قاعدة البيانات للأحداث المهمة
        if ($level === 'error' || strpos($message, 'تم إنشاء') !== false) {
            try {
                $this->activityLogModel->logActivity(
                    null, // مستخدم النظام
                    'مجدول فحص الإشعارات',
                    $message
                );
            } catch (Exception $e) {
                // تجاهل أخطاء تسجيل الأنشطة لتجنب التكرار اللانهائي
            }
        }
    }
}

// تشغيل المجدول
if (php_sapi_name() === 'cli') {
    $checker = new NotificationChecker();
    
    // فحص المعاملات
    $action = $argv[1] ?? 'run';
    
    switch ($action) {
        case 'run':
            $checker->run();
            break;
            
        case 'low_stock':
            $checker->checkLowStock();
            break;
            
        case 'overdue':
            $checker->checkOverdueInvoices();
            break;
            
        case 'backups':
            $checker->checkBackups();
            break;
            
        case 'test':
            $checker->createTestNotification();
            break;
            
        case 'stats':
            $checker->getStats();
            break;
            
        default:
            echo "الاستخدام: php notification_checker.php [run|low_stock|overdue|backups|test|stats]\n";
            echo "\n";
            echo "الخيارات:\n";
            echo "  run        - تشغيل جميع الفحوصات\n";
            echo "  low_stock  - فحص المخزون المنخفض فقط\n";
            echo "  overdue    - فحص الفواتير المستحقة فقط\n";
            echo "  backups    - فحص النسخ الاحتياطية فقط\n";
            echo "  test       - إنشاء إشعار تجريبي\n";
            echo "  stats      - عرض إحصائيات الإشعارات\n";
            exit(1);
    }
}

?>
