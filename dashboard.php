<?php
/**
 * لوحة التحكم الرئيسية
 * Main Dashboard
 */

define('APP_INIT', true);
require_once 'includes/init.php';
require_once 'includes/chart_components.php';

// التحقق من تسجيل الدخول
requireLogin();

// إنشاء مثيل نموذج التحليلات
$analyticsModel = new Analytics();

// الحصول على الفترة المحددة
$period = $_GET['period'] ?? 'month';
$validPeriods = ['today', 'week', 'month', 'quarter', 'year'];
if (!in_array($period, $validPeriods)) {
    $period = 'month';
}

// الحصول على الإحصائيات الشاملة
$stats = $analyticsModel->getComprehensiveStats($period);

// الحصول على الإحصائيات
try {
    $db = Database::getInstance();
    
    // إحصائيات المخزون
    $inventoryStats = $db->selectOne("
        SELECT 
            COUNT(DISTINCT i.item_id) as total_items,
            COUNT(DISTINCT w.warehouse_id) as total_warehouses,
            COALESCE(SUM(ib.total_value), 0) as total_inventory_value,
            COUNT(CASE WHEN ib.quantity <= i.min_stock_level THEN 1 END) as low_stock_items
        FROM items i
        LEFT JOIN inventory_balances ib ON i.item_id = ib.item_id
        LEFT JOIN warehouses w ON ib.warehouse_id = w.warehouse_id
        WHERE i.is_active = 1
    ");
    
    // إحصائيات الحسابات
    $accountStats = $db->selectOne("
        SELECT 
            COUNT(*) as total_accounts,
            COALESCE(SUM(CASE WHEN account_type = 'assets' THEN 1 ELSE 0 END), 0) as assets_accounts,
            COALESCE(SUM(CASE WHEN account_type = 'liabilities' THEN 1 ELSE 0 END), 0) as liabilities_accounts,
            COALESCE(SUM(CASE WHEN account_type = 'revenue' THEN 1 ELSE 0 END), 0) as revenue_accounts
        FROM chart_of_accounts 
        WHERE is_active = 1
    ");
    
    // إحصائيات المعاملات (آخر 30 يوم)
    $transactionStats = $db->selectOne("
        SELECT 
            COUNT(*) as total_transactions,
            COALESCE(SUM(debit_amount), 0) as total_debits,
            COALESCE(SUM(credit_amount), 0) as total_credits
        FROM account_transactions 
        WHERE transaction_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        AND is_posted = 1
    ");
    
    // إحصائيات المستخدمين
    $userStats = $db->selectOne("
        SELECT 
            COUNT(*) as total_users,
            COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_users,
            COUNT(CASE WHEN last_login >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as recent_logins
        FROM users
    ");
    
    // الأصناف منخفضة المخزون
    $lowStockItems = $db->select("
        SELECT 
            i.item_name,
            w.warehouse_name,
            ib.quantity,
            u.unit_symbol,
            i.min_stock_level
        FROM inventory_balances ib
        JOIN items i ON ib.item_id = i.item_id
        JOIN warehouses w ON ib.warehouse_id = w.warehouse_id
        JOIN units u ON ib.unit_id = u.unit_id
        WHERE ib.quantity <= i.min_stock_level
        AND i.is_active = 1
        ORDER BY (ib.quantity / i.min_stock_level) ASC
        LIMIT 10
    ");
    
    // آخر المعاملات
    $recentTransactions = $db->select("
        SELECT 
            at.voucher_number,
            at.transaction_date,
            coa.account_name,
            at.debit_amount,
            at.credit_amount,
            at.description
        FROM account_transactions at
        JOIN chart_of_accounts coa ON at.account_id = coa.account_id
        WHERE at.is_posted = 1
        ORDER BY at.created_at DESC
        LIMIT 10
    ");
    
} catch (Exception $e) {
    error_log("Dashboard stats error: " . $e->getMessage());
    $inventoryStats = $accountStats = $transactionStats = $userStats = [];
    $lowStockItems = $recentTransactions = [];
}

$pageTitle = 'لوحة التحكم الرئيسية';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- خط عربي -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- أيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- CSS الرسوم البيانية -->
    <link href="assets/css/charts.css" rel="stylesheet">

    <!-- CSS للرسوم البيانية البسيطة -->
    <style>
        .progress-bar {
            width: 100%;
            height: 8px;
            background-color: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            transition: width 0.3s ease;
        }

        .progress-success { background-color: #10b981; }
        .progress-warning { background-color: #f59e0b; }
        .progress-error { background-color: #ef4444; }
        .progress-info { background-color: #3b82f6; }
    </style>
    
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .card-hover:hover { transform: translateY(-2px); transition: all 0.3s ease; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }

        /* أنماط إضافية للوحة التحكم */
        .period-selector {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .period-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .period-btn {
            padding: 8px 16px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            color: #374151;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.2s;
        }

        .period-btn:hover {
            background: #f3f4f6;
            border-color: #9ca3af;
        }

        .period-btn.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .dashboard-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #111827;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #e5e7eb;
        }
    </style>
</head>
<body class="bg-gray-50">

    <!-- شريط التنقل العلوي -->
    <nav class="gradient-bg shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="fas fa-calculator text-white text-2xl ml-3"></i>
                        <h1 class="text-white text-xl font-bold"><?php echo APP_NAME; ?></h1>
                    </div>
                </div>
                
                <div class="flex items-center space-x-4 space-x-reverse">
                    <!-- مكون الإشعارات -->
                    <?php include 'includes/notification_widget.php'; ?>

                    <div class="text-white text-sm">
                        <i class="fas fa-user ml-1"></i>
                        مرحباً، <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </div>
                    <a href="logout.php" class="text-white hover:text-gray-200 transition-colors duration-200">
                        <i class="fas fa-sign-out-alt ml-1"></i>
                        تسجيل الخروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        
        <!-- عرض الرسائل -->
        <?php showAlert(); ?>

        <!-- فلتر الفترة الزمنية -->
        <div class="period-selector">
            <h3 class="text-lg font-medium text-gray-900 mb-3 text-center">
                <i class="fas fa-calendar-alt ml-2"></i>
                اختيار الفترة الزمنية
            </h3>
            <div class="period-buttons">
                <?php
                $periodLabels = [
                    'today' => 'اليوم',
                    'week' => 'هذا الأسبوع',
                    'month' => 'هذا الشهر',
                    'quarter' => 'هذا الربع',
                    'year' => 'هذا العام'
                ];

                foreach ($periodLabels as $key => $label) {
                    $activeClass = ($period === $key) ? 'active' : '';
                    echo '<a href="?period=' . $key . '" class="period-btn ' . $activeClass . '">' . $label . '</a>';
                }
                ?>
            </div>
        </div>

        <!-- بطاقات الإحصائيات -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            
            <!-- إجمالي الأصناف -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-boxes text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">إجمالي الأصناف</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?php echo number_format($inventoryStats['total_items'] ?? 0); ?>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- إجمالي المخازن -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-warehouse text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">إجمالي المخازن</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?php echo number_format($inventoryStats['total_warehouses'] ?? 0); ?>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- قيمة المخزون -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-coins text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">قيمة المخزون</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?php echo formatMoney($inventoryStats['total_inventory_value'] ?? 0); ?>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- الأصناف منخفضة المخزون -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-exclamation-triangle text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">مخزون منخفض</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?php echo number_format($inventoryStats['low_stock_items'] ?? 0); ?>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
        </div>
        
        <!-- الشبكة الرئيسية -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            
            <!-- الأصناف منخفضة المخزون -->
            <div class="bg-white shadow-lg rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-exclamation-triangle text-red-500 ml-2"></i>
                        الأصناف منخفضة المخزون
                    </h3>
                </div>
                <div class="p-6">
                    <?php if (empty($lowStockItems)): ?>
                        <div class="text-center py-8">
                            <i class="fas fa-check-circle text-green-500 text-4xl mb-4"></i>
                            <p class="text-gray-500">جميع الأصناف في مستوى آمن</p>
                        </div>
                    <?php else: ?>
                        <div class="space-y-3">
                            <?php foreach ($lowStockItems as $item): ?>
                                <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                                    <div>
                                        <p class="font-medium text-gray-900"><?php echo htmlspecialchars($item['item_name']); ?></p>
                                        <p class="text-sm text-gray-500"><?php echo htmlspecialchars($item['warehouse_name']); ?></p>
                                    </div>
                                    <div class="text-left">
                                        <p class="text-sm font-medium text-red-600">
                                            <?php echo formatNumber($item['quantity'], 3); ?> <?php echo htmlspecialchars($item['unit_symbol']); ?>
                                        </p>
                                        <p class="text-xs text-gray-500">
                                            الحد الأدنى: <?php echo formatNumber($item['min_stock_level'], 3); ?>
                                        </p>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- آخر المعاملات -->
            <div class="bg-white shadow-lg rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-history text-blue-500 ml-2"></i>
                        آخر المعاملات المحاسبية
                    </h3>
                </div>
                <div class="p-6">
                    <?php if (empty($recentTransactions)): ?>
                        <div class="text-center py-8">
                            <i class="fas fa-file-invoice text-gray-400 text-4xl mb-4"></i>
                            <p class="text-gray-500">لا توجد معاملات حديثة</p>
                        </div>
                    <?php else: ?>
                        <div class="space-y-3">
                            <?php foreach ($recentTransactions as $transaction): ?>
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div>
                                        <p class="font-medium text-gray-900"><?php echo htmlspecialchars($transaction['account_name']); ?></p>
                                        <p class="text-sm text-gray-500"><?php echo htmlspecialchars($transaction['description']); ?></p>
                                        <p class="text-xs text-gray-400">
                                            <?php echo formatDate($transaction['transaction_date']); ?> - 
                                            قيد رقم: <?php echo htmlspecialchars($transaction['voucher_number']); ?>
                                        </p>
                                    </div>
                                    <div class="text-left">
                                        <?php if ($transaction['debit_amount'] > 0): ?>
                                            <p class="text-sm font-medium text-green-600">
                                                مدين: <?php echo formatMoney($transaction['debit_amount']); ?>
                                            </p>
                                        <?php endif; ?>
                                        <?php if ($transaction['credit_amount'] > 0): ?>
                                            <p class="text-sm font-medium text-red-600">
                                                دائن: <?php echo formatMoney($transaction['credit_amount']); ?>
                                            </p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
        </div>
        
        <!-- روابط سريعة -->
        <div class="mt-8">
            <h3 class="text-lg font-medium text-gray-900 mb-4">
                <i class="fas fa-bolt text-yellow-500 ml-2"></i>
                الوصول السريع
            </h3>
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                
                <a href="items.php" class="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow duration-200 text-center">
                    <i class="fas fa-boxes text-blue-500 text-2xl mb-2"></i>
                    <p class="text-sm font-medium text-gray-900">إدارة الأصناف</p>
                </a>
                
                <a href="warehouses.php" class="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow duration-200 text-center">
                    <i class="fas fa-warehouse text-green-500 text-2xl mb-2"></i>
                    <p class="text-sm font-medium text-gray-900">إدارة المخازن</p>
                </a>

                <a href="inventory.php" class="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow duration-200 text-center">
                    <i class="fas fa-boxes text-indigo-500 text-2xl mb-2"></i>
                    <p class="text-sm font-medium text-gray-900">أرصدة المخزون</p>
                </a>

                <a href="accounts.php" class="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow duration-200 text-center">
                    <i class="fas fa-chart-line text-purple-500 text-2xl mb-2"></i>
                    <p class="text-sm font-medium text-gray-900">شجرة الحسابات</p>
                </a>

                <a href="transactions.php" class="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow duration-200 text-center">
                    <i class="fas fa-exchange-alt text-orange-500 text-2xl mb-2"></i>
                    <p class="text-sm font-medium text-gray-900">القيود المحاسبية</p>
                </a>

                <a href="customers.php" class="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow duration-200 text-center">
                    <i class="fas fa-users text-teal-500 text-2xl mb-2"></i>
                    <p class="text-sm font-medium text-gray-900">العملاء والموردين</p>
                </a>

                <a href="invoices.php" class="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow duration-200 text-center">
                    <i class="fas fa-file-invoice text-pink-500 text-2xl mb-2"></i>
                    <p class="text-sm font-medium text-gray-900">الفواتير</p>
                </a>

                <a href="reports.php" class="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow duration-200 text-center">
                    <i class="fas fa-chart-line text-red-500 text-2xl mb-2"></i>
                    <p class="text-sm font-medium text-gray-900">التقارير</p>
                </a>

                <a href="users.php" class="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow duration-200 text-center">
                    <i class="fas fa-users text-indigo-500 text-2xl mb-2"></i>
                    <p class="text-sm font-medium text-gray-900">المستخدمين</p>
                </a>

                <a href="roles.php" class="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow duration-200 text-center">
                    <i class="fas fa-user-shield text-orange-500 text-2xl mb-2"></i>
                    <p class="text-sm font-medium text-gray-900">الأدوار والصلاحيات</p>
                </a>

                <a href="activity_log.php" class="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow duration-200 text-center">
                    <i class="fas fa-history text-gray-500 text-2xl mb-2"></i>
                    <p class="text-sm font-medium text-gray-900">سجل الأنشطة</p>
                </a>

                <a href="backup.php" class="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow duration-200 text-center">
                    <i class="fas fa-database text-teal-500 text-2xl mb-2"></i>
                    <p class="text-sm font-medium text-gray-900">النسخ الاحتياطي</p>
                </a>

                <a href="restore.php" class="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow duration-200 text-center">
                    <i class="fas fa-upload text-cyan-500 text-2xl mb-2"></i>
                    <p class="text-sm font-medium text-gray-900">استعادة النسخ</p>
                </a>

                <a href="notifications.php" class="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow duration-200 text-center">
                    <i class="fas fa-bell text-yellow-500 text-2xl mb-2"></i>
                    <p class="text-sm font-medium text-gray-900">الإشعارات</p>
                </a>

                <a href="settings.php" class="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow duration-200 text-center">
                    <i class="fas fa-cog text-indigo-500 text-2xl mb-2"></i>
                    <p class="text-sm font-medium text-gray-900">إعدادات النظام</p>
                </a>

                <a href="print_templates.php" class="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow duration-200 text-center">
                    <i class="fas fa-print text-purple-500 text-2xl mb-2"></i>
                    <p class="text-sm font-medium text-gray-900">قوالب الطباعة</p>
                </a>

                <a href="advanced_reports.php" class="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow duration-200 text-center">
                    <i class="fas fa-chart-bar text-cyan-500 text-2xl mb-2"></i>
                    <p class="text-sm font-medium text-gray-900">التقارير المتقدمة</p>
                </a>

                <a href="attachments.php" class="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow duration-200 text-center">
                    <i class="fas fa-paperclip text-orange-500 text-2xl mb-2"></i>
                    <p class="text-sm font-medium text-gray-900">إدارة المرفقات</p>
                </a>

                <?php if (hasPermission('system_admin')): ?>
                <a href="advanced_roles.php" class="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow duration-200 text-center">
                    <i class="fas fa-user-shield text-indigo-500 text-2xl mb-2"></i>
                    <p class="text-sm font-medium text-gray-900">إدارة الأدوار</p>
                </a>

                <a href="user_roles.php" class="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow duration-200 text-center">
                    <i class="fas fa-users-cog text-teal-500 text-2xl mb-2"></i>
                    <p class="text-sm font-medium text-gray-900">تعيين الأدوار</p>
                </a>
                <?php endif; ?>
                
                <a href="accounts.php" class="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow duration-200 text-center">
                    <i class="fas fa-chart-line text-purple-500 text-2xl mb-2"></i>
                    <p class="text-sm font-medium text-gray-900">شجرة الحسابات</p>
                </a>
                
                <a href="transactions.php" class="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow duration-200 text-center">
                    <i class="fas fa-exchange-alt text-orange-500 text-2xl mb-2"></i>
                    <p class="text-sm font-medium text-gray-900">القيود المحاسبية</p>
                </a>
                
                <a href="reports.php" class="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow duration-200 text-center">
                    <i class="fas fa-chart-bar text-red-500 text-2xl mb-2"></i>
                    <p class="text-sm font-medium text-gray-900">التقارير</p>
                </a>
                
                <a href="settings.php" class="bg-white p-4 rounded-lg shadow hover:shadow-md transition-shadow duration-200 text-center">
                    <i class="fas fa-cog text-gray-500 text-2xl mb-2"></i>
                    <p class="text-sm font-medium text-gray-900">الإعدادات</p>
                </a>
                
            </div>
        </div>

        <!-- الإحصائيات المتقدمة -->
        <div class="dashboard-section">
            <h2 class="section-title">
                <i class="fas fa-chart-bar ml-2"></i>
                الإحصائيات المتقدمة
            </h2>

            <div class="charts-grid">

                <!-- رسم بياني للمبيعات اليومية -->
                <div class="chart-container">
                    <?php
                    $dailySalesData = [];
                    foreach ($stats['daily_sales'] as $sale) {
                        $dailySalesData[formatDate($sale['sale_date'])] = $sale['daily_sales'];
                    }
                    echo renderLineChart($dailySalesData, 'المبيعات اليومية (آخر 7 أيام)', '#10b981');
                    ?>
                </div>

                <!-- رسم بياني دائري لحالة الدفع -->
                <div class="chart-container">
                    <?php
                    $paymentStatusData = [
                        'مدفوع' => $stats['sales']['paid_amount'] ?? 0,
                        'معلق' => $stats['sales']['pending_amount'] ?? 0,
                        'متأخر' => $stats['sales']['overdue_amount'] ?? 0
                    ];
                    echo renderPieChart($paymentStatusData, 'حالة المدفوعات', ['#10b981', '#f59e0b', '#ef4444']);
                    ?>
                </div>

                <!-- أفضل الأصناف مبيعاً -->
                <div class="chart-container">
                    <?php
                    $topItemsData = [];
                    foreach ($stats['top_items'] as $item) {
                        $topItemsData[$item['item_name']] = $item['total_sales'];
                    }
                    echo renderBarChart($topItemsData, 'أفضل الأصناف مبيعاً', '#3b82f6');
                    ?>
                </div>

                <!-- مؤشر المخزون -->
                <div class="chart-container">
                    <?php
                    $totalItems = $stats['inventory']['total_items'] ?? 1;
                    $inStockItems = $stats['inventory']['items_in_stock'] ?? 0;
                    $stockPercentage = ($inStockItems / $totalItems) * 100;
                    echo renderProgressCircle($stockPercentage, 'نسبة الأصناف المتوفرة', '#8b5cf6');
                    ?>
                </div>

            </div>
        </div>

        <!-- جداول التفاصيل -->
        <div class="dashboard-section">
            <h2 class="section-title">
                <i class="fas fa-table ml-2"></i>
                التفاصيل والتقارير
            </h2>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">

                <!-- أفضل العملاء -->
                <div class="chart-container">
                    <?php
                    $topCustomersData = [];
                    foreach ($stats['top_customers'] as $customer) {
                        $topCustomersData[] = [
                            $customer['customer_name'],
                            formatMoney($customer['total_purchases']),
                            $customer['invoice_count'] . ' فاتورة'
                        ];
                    }
                    echo renderStatsTable($topCustomersData, 'أفضل العملاء', ['العميل', 'إجمالي المشتريات', 'عدد الفواتير']);
                    ?>
                </div>

                <!-- الأصناف منخفضة المخزون -->
                <div class="chart-container">
                    <?php
                    $lowStockData = [];
                    foreach ($stats['low_stock'] as $item) {
                        $lowStockData[] = [
                            $item['item_name'],
                            $item['current_stock'] . ' ' . ($item['unit_name'] ?? ''),
                            $item['minimum_stock'] . ' ' . ($item['unit_name'] ?? '')
                        ];
                    }
                    echo renderStatsTable($lowStockData, 'الأصناف منخفضة المخزون', ['الصنف', 'المخزون الحالي', 'الحد الأدنى']);
                    ?>
                </div>

            </div>
        </div>

    </div>

    <!-- تذييل الصفحة -->
    <footer class="bg-white border-t border-gray-200 mt-12">
        <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center">
                <p class="text-sm text-gray-500">
                    © 2025 <?php echo APP_NAME; ?> - الإصدار <?php echo APP_VERSION; ?>
                </p>
                <p class="text-sm text-gray-500">
                    <i class="fas fa-code ml-1"></i>
                    تطوير فريق فيزي سوفت
                </p>
            </div>
        </div>
    </footer>

</body>
</html>
