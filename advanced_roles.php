<?php
/**
 * صفحة إدارة الأدوار المتقدمة
 * Advanced Roles Management Page
 */

define('APP_INIT', true);
require_once 'includes/init.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('system_admin');

// إنشاء مثيل نموذج الأدوار المتقدم
$advancedRoleModel = new AdvancedRole();

// معالجة العمليات
$action = $_GET['action'] ?? 'list';
$roleId = $_GET['id'] ?? null;
$message = '';
$messageType = '';

// معالجة إنشاء/تحديث دور
if (($_SERVER['REQUEST_METHOD'] === 'POST') && in_array($action, ['create', 'edit'])) {
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    if (verifyCSRFToken($csrfToken)) {
        $roleData = [
            'role_name' => sanitizeInput($_POST['role_name'] ?? ''),
            'role_description' => sanitizeInput($_POST['role_description'] ?? ''),
            'role_type' => sanitizeInput($_POST['role_type'] ?? 'custom'),
            'is_active' => isset($_POST['is_active']) ? 1 : 0
        ];
        
        if ($action === 'create') {
            $result = $advancedRoleModel->createRole($roleData);
            if ($result) {
                $message = 'تم إنشاء الدور بنجاح';
                $messageType = 'success';
                $action = 'list';
            } else {
                $message = 'فشل في إنشاء الدور';
                $messageType = 'error';
            }
        } else {
            $result = $advancedRoleModel->updateRole($roleId, $roleData);
            if ($result) {
                $message = 'تم تحديث الدور بنجاح';
                $messageType = 'success';
                $action = 'list';
            } else {
                $message = 'فشل في تحديث الدور';
                $messageType = 'error';
            }
        }
    } else {
        $message = 'طلب غير صالح';
        $messageType = 'error';
    }
}

// معالجة حذف دور
if ($action === 'delete' && $roleId && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    if (verifyCSRFToken($csrfToken)) {
        if ($advancedRoleModel->deleteRole($roleId)) {
            $message = 'تم حذف الدور بنجاح';
            $messageType = 'success';
        } else {
            $message = 'فشل في حذف الدور أو أن الدور مرتبط بمستخدمين';
            $messageType = 'error';
        }
        $action = 'list';
    } else {
        $message = 'طلب غير صالح';
        $messageType = 'error';
    }
}

// معالجة تعيين صلاحيات
if ($action === 'assign_permissions' && $roleId && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    if (verifyCSRFToken($csrfToken)) {
        $permissions = [];
        
        // معالجة صلاحيات الوحدات
        if (isset($_POST['module_permissions'])) {
            foreach ($_POST['module_permissions'] as $module => $level) {
                if ($level !== 'none') {
                    $permissions[] = [
                        'permission_type' => 'module',
                        'permission_key' => $module,
                        'access_level' => $level
                    ];
                }
            }
        }
        
        // معالجة صلاحيات العمليات
        if (isset($_POST['action_permissions'])) {
            foreach ($_POST['action_permissions'] as $actionKey => $level) {
                if ($level !== 'none') {
                    $permissions[] = [
                        'permission_type' => 'action',
                        'permission_key' => $actionKey,
                        'access_level' => $level
                    ];
                }
            }
        }
        
        // معالجة صلاحيات الحقول
        if (isset($_POST['field_permissions'])) {
            foreach ($_POST['field_permissions'] as $field => $level) {
                if ($level !== 'none') {
                    $permissions[] = [
                        'permission_type' => 'field',
                        'permission_key' => $field,
                        'access_level' => $level
                    ];
                }
            }
        }
        
        if ($advancedRoleModel->assignPermissions($roleId, $permissions)) {
            $message = 'تم تعيين الصلاحيات بنجاح';
            $messageType = 'success';
        } else {
            $message = 'فشل في تعيين الصلاحيات';
            $messageType = 'error';
        }
    } else {
        $message = 'طلب غير صالح';
        $messageType = 'error';
    }
}

// الحصول على البيانات حسب العملية
$roles = [];
$currentRole = null;
$rolePermissions = [];

if ($action === 'list') {
    $roles = $advancedRoleModel->getAllRoles(true);
} elseif (in_array($action, ['edit', 'permissions']) && $roleId) {
    $currentRole = $advancedRoleModel->getRoleById($roleId);
    if (!$currentRole) {
        $message = 'الدور غير موجود';
        $messageType = 'error';
        $action = 'list';
        $roles = $advancedRoleModel->getAllRoles(true);
    } else {
        $rolePermissions = $advancedRoleModel->getRolePermissions($roleId);
    }
}

// تعريف الوحدات والعمليات والحقول المتاحة
$availableModules = [
    'dashboard' => 'لوحة التحكم',
    'invoices' => 'الفواتير',
    'customers' => 'العملاء',
    'suppliers' => 'الموردين',
    'items' => 'الأصناف',
    'inventory' => 'المخزون',
    'reports' => 'التقارير',
    'accounting' => 'المحاسبة',
    'users' => 'المستخدمين',
    'settings' => 'الإعدادات',
    'attachments' => 'المرفقات'
];

$availableActions = [
    'create_invoice' => 'إنشاء فاتورة',
    'edit_invoice' => 'تحرير فاتورة',
    'delete_invoice' => 'حذف فاتورة',
    'approve_invoice' => 'اعتماد فاتورة',
    'print_invoice' => 'طباعة فاتورة',
    'manage_customers' => 'إدارة العملاء',
    'manage_suppliers' => 'إدارة الموردين',
    'manage_items' => 'إدارة الأصناف',
    'stock_movements' => 'حركات المخزون',
    'financial_reports' => 'التقارير المالية',
    'system_backup' => 'نسخ احتياطي',
    'user_management' => 'إدارة المستخدمين'
];

$availableFields = [
    'invoice_amounts' => 'مبالغ الفواتير',
    'customer_info' => 'معلومات العملاء',
    'supplier_info' => 'معلومات الموردين',
    'item_prices' => 'أسعار الأصناف',
    'stock_quantities' => 'كميات المخزون',
    'financial_data' => 'البيانات المالية',
    'user_data' => 'بيانات المستخدمين',
    'system_settings' => 'إعدادات النظام'
];

$pageTitle = 'إدارة الأدوار المتقدمة';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- خط عربي -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- أيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        
        .permission-grid {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
            gap: 10px;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .permission-grid:hover {
            background-color: #f9fafb;
        }
        
        .permission-header {
            background-color: #f3f4f6;
            font-weight: 600;
            color: #374151;
        }
        
        .access-level-select {
            padding: 4px 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .role-card {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            transition: all 0.2s;
        }
        
        .role-card:hover {
            border-color: #3b82f6;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .role-type-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .role-type-system {
            background-color: #fef3c7;
            color: #92400e;
        }
        
        .role-type-functional {
            background-color: #dbeafe;
            color: #1e40af;
        }
        
        .role-type-custom {
            background-color: #f3e8ff;
            color: #7c3aed;
        }
    </style>
</head>
<body class="bg-gray-50">

    <!-- شريط التنقل العلوي -->
    <nav class="gradient-bg shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="dashboard.php" class="flex-shrink-0 flex items-center text-white hover:text-gray-200">
                        <i class="fas fa-arrow-right text-xl ml-3"></i>
                        <span>العودة للوحة التحكم</span>
                    </a>
                </div>

                <div class="flex items-center">
                    <h1 class="text-white text-xl font-bold">
                        <i class="fas fa-user-shield ml-2"></i>
                        <?php echo $pageTitle; ?>
                    </h1>
                </div>

                <div class="flex items-center space-x-4 space-x-reverse">
                    <!-- مكون الإشعارات -->
                    <?php include 'includes/notification_widget.php'; ?>

                    <div class="text-white text-sm">
                        <i class="fas fa-user ml-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </div>
                    <a href="logout.php" class="text-white hover:text-gray-200 transition-colors duration-200">
                        <i class="fas fa-sign-out-alt ml-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">

        <!-- عرض الرسائل -->
        <?php if (!empty($message)): ?>
            <div class="mb-6">
                <div class="<?php echo $messageType === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 'bg-red-100 border border-red-400 text-red-700'; ?> px-4 py-3 rounded">
                    <i class="fas <?php echo $messageType === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?> ml-2"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($action === 'list'): ?>

            <!-- قائمة الأدوار -->
            <div class="bg-white shadow-lg rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">
                            <i class="fas fa-list ml-2"></i>
                            الأدوار المتاحة
                        </h3>
                        <a href="?action=create" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-plus ml-2"></i>
                            إنشاء دور جديد
                        </a>
                    </div>
                </div>

                <div class="p-6">
                    <?php if (empty($roles)): ?>
                        <div class="text-center py-12">
                            <i class="fas fa-user-shield text-gray-400 text-6xl mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد أدوار</h3>
                            <p class="text-gray-500">لم يتم إنشاء أي أدوار بعد</p>
                        </div>
                    <?php else: ?>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <?php foreach ($roles as $role): ?>
                                <div class="role-card">
                                    <div class="flex justify-between items-start mb-4">
                                        <div>
                                            <h4 class="text-lg font-semibold text-gray-900"><?php echo htmlspecialchars($role['role_name']); ?></h4>
                                            <p class="text-sm text-gray-600 mt-1"><?php echo htmlspecialchars($role['role_description']); ?></p>
                                        </div>
                                        <span class="role-type-badge role-type-<?php echo $role['role_type']; ?>">
                                            <?php
                                            $typeLabels = ['system' => 'نظام', 'functional' => 'وظيفي', 'custom' => 'مخصص'];
                                            echo $typeLabels[$role['role_type']] ?? $role['role_type'];
                                            ?>
                                        </span>
                                    </div>

                                    <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                        <span>
                                            <i class="fas fa-users ml-1"></i>
                                            <?php echo $role['users_count']; ?> مستخدم
                                        </span>
                                        <span class="<?php echo $role['is_active'] ? 'text-green-600' : 'text-red-600'; ?>">
                                            <i class="fas <?php echo $role['is_active'] ? 'fa-check-circle' : 'fa-times-circle'; ?> ml-1"></i>
                                            <?php echo $role['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                        </span>
                                    </div>

                                    <div class="flex justify-between items-center">
                                        <div class="flex space-x-2 space-x-reverse">
                                            <a href="?action=permissions&id=<?php echo $role['role_id']; ?>"
                                               class="text-blue-600 hover:text-blue-800 text-sm" title="إدارة الصلاحيات">
                                                <i class="fas fa-key"></i>
                                            </a>

                                            <?php if (!$role['is_system_role']): ?>
                                                <a href="?action=edit&id=<?php echo $role['role_id']; ?>"
                                                   class="text-green-600 hover:text-green-800 text-sm" title="تحرير">
                                                    <i class="fas fa-edit"></i>
                                                </a>

                                                <form method="POST" action="?action=delete&id=<?php echo $role['role_id']; ?>"
                                                      class="inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا الدور؟')">
                                                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                    <button type="submit" class="text-red-600 hover:text-red-800 text-sm" title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            <?php else: ?>
                                                <span class="text-gray-400 text-sm" title="دور نظام - لا يمكن تحريره">
                                                    <i class="fas fa-lock"></i>
                                                </span>
                                            <?php endif; ?>
                                        </div>

                                        <div class="text-xs text-gray-400">
                                            <?php echo formatDateTime($role['created_at']); ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

        <?php elseif (in_array($action, ['create', 'edit'])): ?>

            <!-- نموذج إنشاء/تحرير دور -->
            <div class="bg-white shadow-lg rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas <?php echo $action === 'create' ? 'fa-plus' : 'fa-edit'; ?> ml-2"></i>
                        <?php echo $action === 'create' ? 'إنشاء دور جديد' : 'تحرير الدور'; ?>
                    </h3>
                </div>

                <div class="p-6">
                    <form method="POST" action="?action=<?php echo $action; ?><?php echo $roleId ? '&id=' . $roleId : ''; ?>">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

                            <!-- اسم الدور -->
                            <div>
                                <label for="role_name" class="block text-sm font-medium text-gray-700 mb-2">
                                    اسم الدور <span class="text-red-500">*</span>
                                </label>
                                <input type="text" id="role_name" name="role_name" required
                                       value="<?php echo htmlspecialchars($currentRole['role_name'] ?? ''); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>

                            <!-- نوع الدور -->
                            <div>
                                <label for="role_type" class="block text-sm font-medium text-gray-700 mb-2">
                                    نوع الدور
                                </label>
                                <select id="role_type" name="role_type"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="custom" <?php echo ($currentRole['role_type'] ?? 'custom') === 'custom' ? 'selected' : ''; ?>>مخصص</option>
                                    <option value="functional" <?php echo ($currentRole['role_type'] ?? '') === 'functional' ? 'selected' : ''; ?>>وظيفي</option>
                                </select>
                            </div>

                        </div>

                        <!-- وصف الدور -->
                        <div class="mt-6">
                            <label for="role_description" class="block text-sm font-medium text-gray-700 mb-2">
                                وصف الدور
                            </label>
                            <textarea id="role_description" name="role_description" rows="3"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"><?php echo htmlspecialchars($currentRole['role_description'] ?? ''); ?></textarea>
                        </div>

                        <!-- حالة الدور -->
                        <div class="mt-6">
                            <div class="flex items-center">
                                <input type="checkbox" id="is_active" name="is_active" value="1"
                                       <?php echo ($currentRole['is_active'] ?? 1) ? 'checked' : ''; ?>
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="is_active" class="mr-2 block text-sm text-gray-900">
                                    دور نشط
                                </label>
                            </div>
                        </div>

                        <!-- أزرار الإجراءات -->
                        <div class="mt-8 flex justify-between">
                            <a href="?action=list" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-times ml-2"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                                <i class="fas fa-save ml-2"></i>
                                <?php echo $action === 'create' ? 'إنشاء الدور' : 'حفظ التغييرات'; ?>
                            </button>
                        </div>

                    </form>
                </div>
            </div>

        <?php elseif ($action === 'permissions' && $currentRole): ?>

            <!-- إدارة صلاحيات الدور -->
            <div class="bg-white shadow-lg rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">
                            <i class="fas fa-key ml-2"></i>
                            صلاحيات الدور: <?php echo htmlspecialchars($currentRole['role_name']); ?>
                        </h3>
                        <a href="?action=list" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-arrow-right ml-2"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>

                <div class="p-6">
                    <form method="POST" action="?action=assign_permissions&id=<?php echo $roleId; ?>">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                        <!-- صلاحيات الوحدات -->
                        <div class="mb-8">
                            <h4 class="text-lg font-semibold text-gray-900 mb-4">
                                <i class="fas fa-th-large ml-2"></i>
                                صلاحيات الوحدات
                            </h4>

                            <div class="border border-gray-200 rounded-lg overflow-hidden">
                                <div class="permission-grid permission-header">
                                    <div>الوحدة</div>
                                    <div class="text-center">قراءة</div>
                                    <div class="text-center">كتابة</div>
                                    <div class="text-center">حذف</div>
                                    <div class="text-center">إدارة كاملة</div>
                                </div>

                                <?php
                                // تجميع الصلاحيات الحالية
                                $currentPermissions = [];
                                foreach ($rolePermissions as $perm) {
                                    $currentPermissions[$perm['permission_type']][$perm['permission_key']] = $perm['access_level'];
                                }

                                foreach ($availableModules as $moduleKey => $moduleName):
                                    $currentLevel = $currentPermissions['module'][$moduleKey] ?? 'none';
                                ?>
                                    <div class="permission-grid">
                                        <div class="font-medium"><?php echo $moduleName; ?></div>
                                        <div class="text-center">
                                            <input type="radio" name="module_permissions[<?php echo $moduleKey; ?>]" value="read"
                                                   <?php echo $currentLevel === 'read' ? 'checked' : ''; ?>>
                                        </div>
                                        <div class="text-center">
                                            <input type="radio" name="module_permissions[<?php echo $moduleKey; ?>]" value="write"
                                                   <?php echo $currentLevel === 'write' ? 'checked' : ''; ?>>
                                        </div>
                                        <div class="text-center">
                                            <input type="radio" name="module_permissions[<?php echo $moduleKey; ?>]" value="delete"
                                                   <?php echo $currentLevel === 'delete' ? 'checked' : ''; ?>>
                                        </div>
                                        <div class="text-center">
                                            <input type="radio" name="module_permissions[<?php echo $moduleKey; ?>]" value="admin"
                                                   <?php echo $currentLevel === 'admin' ? 'checked' : ''; ?>>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <!-- صلاحيات العمليات -->
                        <div class="mb-8">
                            <h4 class="text-lg font-semibold text-gray-900 mb-4">
                                <i class="fas fa-cogs ml-2"></i>
                                صلاحيات العمليات
                            </h4>

                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                <?php foreach ($availableActions as $actionKey => $actionName):
                                    $currentLevel = $currentPermissions['action'][$actionKey] ?? 'none';
                                ?>
                                    <div class="border border-gray-200 rounded-lg p-4">
                                        <h5 class="font-medium text-gray-900 mb-2"><?php echo $actionName; ?></h5>
                                        <select name="action_permissions[<?php echo $actionKey; ?>]" class="access-level-select w-full">
                                            <option value="none" <?php echo $currentLevel === 'none' ? 'selected' : ''; ?>>لا يوجد</option>
                                            <option value="read" <?php echo $currentLevel === 'read' ? 'selected' : ''; ?>>قراءة</option>
                                            <option value="write" <?php echo $currentLevel === 'write' ? 'selected' : ''; ?>>كتابة</option>
                                            <option value="admin" <?php echo $currentLevel === 'admin' ? 'selected' : ''; ?>>إدارة كاملة</option>
                                        </select>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <!-- صلاحيات الحقول -->
                        <div class="mb-8">
                            <h4 class="text-lg font-semibold text-gray-900 mb-4">
                                <i class="fas fa-list-alt ml-2"></i>
                                صلاحيات الحقول
                            </h4>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <?php foreach ($availableFields as $fieldKey => $fieldName):
                                    $currentLevel = $currentPermissions['field'][$fieldKey] ?? 'none';
                                ?>
                                    <div class="border border-gray-200 rounded-lg p-4">
                                        <h5 class="font-medium text-gray-900 mb-2"><?php echo $fieldName; ?></h5>
                                        <select name="field_permissions[<?php echo $fieldKey; ?>]" class="access-level-select w-full">
                                            <option value="none" <?php echo $currentLevel === 'none' ? 'selected' : ''; ?>>لا يوجد</option>
                                            <option value="read" <?php echo $currentLevel === 'read' ? 'selected' : ''; ?>>قراءة</option>
                                            <option value="write" <?php echo $currentLevel === 'write' ? 'selected' : ''; ?>>كتابة</option>
                                        </select>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <!-- زر الحفظ -->
                        <div class="text-center">
                            <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-8 py-3 rounded-lg transition-colors duration-200">
                                <i class="fas fa-save ml-2"></i>
                                حفظ الصلاحيات
                            </button>
                        </div>

                    </form>
                </div>
            </div>

        <?php endif; ?>

    </div>

</body>
</html>
