<?php
/**
 * صفحة أرصدة المخزون
 * Inventory Balances Page
 */

define('APP_INIT', true);
require_once 'includes/init.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('inventory_manage');

// إنشاء مثيلات النماذج
$inventoryModel = new Inventory();
$warehouseModel = new Warehouse();
$itemModel = new Item();

// معالجة المرشحات
$warehouseFilter = !empty($_GET['warehouse']) ? (int)$_GET['warehouse'] : null;
$searchTerm = sanitizeInput($_GET['search'] ?? '');
$lowStockOnly = isset($_GET['low_stock']);
$page = max(1, (int)($_GET['page'] ?? 1));
$itemsPerPage = 50;
$offset = ($page - 1) * $itemsPerPage;

// الحصول على أرصدة المخزون
if (!empty($searchTerm)) {
    $inventoryBalances = $inventoryModel->searchInventory($searchTerm, $warehouseFilter);
    $totalItems = count($inventoryBalances);
} else {
    $inventoryBalances = $inventoryModel->getInventoryBalances($warehouseFilter, null, $lowStockOnly);
    $totalItems = count($inventoryBalances);
    
    // تطبيق الترقيم
    $inventoryBalances = array_slice($inventoryBalances, $offset, $itemsPerPage);
}

$totalPages = ceil($totalItems / $itemsPerPage);

// الحصول على قوائم المساعدة
$warehouses = $warehouseModel->getAllWarehouses();
$inventoryStats = $inventoryModel->getInventoryStats();

$pageTitle = 'أرصدة المخزون';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- خط عربي -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- أيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .card-hover:hover { transform: translateY(-2px); transition: all 0.3s ease; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        
        /* تحسين عرض الجداول */
        .table-responsive {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }
        
        .status-low { background-color: #fef3c7; color: #92400e; }
        .status-out { background-color: #fee2e2; color: #991b1b; }
        .status-normal { background-color: #d1fae5; color: #065f46; }
        .status-high { background-color: #dbeafe; color: #1e40af; }
    </style>
</head>
<body class="bg-gray-50">

    <!-- شريط التنقل العلوي -->
    <nav class="gradient-bg shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="dashboard.php" class="flex-shrink-0 flex items-center text-white hover:text-gray-200">
                        <i class="fas fa-arrow-right text-xl ml-3"></i>
                        <span>العودة للوحة التحكم</span>
                    </a>
                </div>
                
                <div class="flex items-center">
                    <h1 class="text-white text-xl font-bold">
                        <i class="fas fa-boxes ml-2"></i>
                        <?php echo $pageTitle; ?>
                    </h1>
                </div>
                
                <div class="flex items-center space-x-4 space-x-reverse">
                    <span class="text-white text-sm">
                        <i class="fas fa-user ml-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </span>
                    <a href="logout.php" class="text-white hover:text-gray-200 transition-colors duration-200">
                        <i class="fas fa-sign-out-alt ml-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        
        <!-- عرض الرسائل -->
        <?php showAlert(); ?>
        
        <!-- بطاقات الإحصائيات -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            
            <!-- إجمالي الأصناف -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-boxes text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">إجمالي الأصناف</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?php echo number_format($inventoryStats['total_items']); ?>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- إجمالي المخازن -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-warehouse text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">إجمالي المخازن</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?php echo number_format($inventoryStats['total_warehouses']); ?>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- قيمة المخزون -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-coins text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">قيمة المخزون</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?php echo formatMoney($inventoryStats['total_value']); ?>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- مخزون منخفض -->
            <div class="bg-white overflow-hidden shadow-lg rounded-lg card-hover">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-exclamation-triangle text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="mr-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">مخزون منخفض</dt>
                                <dd class="text-lg font-medium text-gray-900">
                                    <?php echo number_format($inventoryStats['low_stock_items']); ?>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
        </div>
        
        <!-- جدول أرصدة المخزون -->
        <div class="bg-white shadow-lg rounded-lg">
            
            <!-- رأس الجدول -->
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900">
                    <i class="fas fa-list ml-2"></i>
                    أرصدة المخزون (<?php echo number_format($totalItems); ?> صنف)
                </h3>
                <div class="flex space-x-2 space-x-reverse">
                    <a href="inventory_movements.php" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                        <i class="fas fa-exchange-alt ml-2"></i>
                        حركات المخزون
                    </a>
                    <a href="warehouses.php" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                        <i class="fas fa-warehouse ml-2"></i>
                        إدارة المخازن
                    </a>
                </div>
            </div>
            
            <!-- شريط البحث والفلترة -->
            <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
                <form method="GET" action="inventory.php" class="flex flex-wrap gap-4">
                    <div class="flex-1 min-w-64">
                        <input 
                            type="text" 
                            name="search" 
                            value="<?php echo htmlspecialchars($searchTerm); ?>"
                            placeholder="البحث في الأصناف (الاسم، الكود)"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                    </div>
                    <div class="min-w-48">
                        <select name="warehouse" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">جميع المخازن</option>
                            <?php foreach ($warehouses as $warehouse): ?>
                                <option value="<?php echo $warehouse['warehouse_id']; ?>" 
                                        <?php echo $warehouseFilter == $warehouse['warehouse_id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($warehouse['warehouse_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="flex items-center">
                        <input 
                            type="checkbox" 
                            id="low_stock" 
                            name="low_stock" 
                            value="1"
                            <?php echo $lowStockOnly ? 'checked' : ''; ?>
                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        >
                        <label for="low_stock" class="mr-2 text-sm text-gray-700">
                            مخزون منخفض فقط
                        </label>
                    </div>
                    <button type="submit" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                        <i class="fas fa-search ml-2"></i>
                        بحث
                    </button>
                    <?php if (!empty($searchTerm) || $warehouseFilter || $lowStockOnly): ?>
                        <a href="inventory.php" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-times ml-2"></i>
                            مسح
                        </a>
                    <?php endif; ?>
                </form>
            </div>

            <!-- جدول الأرصدة -->
            <div class="table-responsive">
                <?php if (empty($inventoryBalances)): ?>
                    <div class="text-center py-12">
                        <i class="fas fa-box-open text-gray-400 text-6xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد أرصدة مخزون</h3>
                        <p class="text-gray-500 mb-4">لم يتم العثور على أي أرصدة مطابقة للبحث</p>
                        <a href="warehouses.php" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-warehouse ml-2"></i>
                            إدارة المخازن
                        </a>
                    </div>
                <?php else: ?>
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الصنف</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المخزن</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الكمية المتاحة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الكمية المحجوزة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">متوسط التكلفة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">إجمالي القيمة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">حالة المخزون</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">آخر حركة</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($inventoryBalances as $balance): ?>
                                <?php
                                // تحديد حالة المخزون
                                $stockStatus = 'normal';
                                $stockLabel = 'طبيعي';
                                $stockClass = 'status-normal';

                                if ($balance['quantity'] == 0) {
                                    $stockStatus = 'out';
                                    $stockLabel = 'نفد';
                                    $stockClass = 'status-out';
                                } elseif ($balance['quantity'] <= $balance['min_stock_level']) {
                                    $stockStatus = 'low';
                                    $stockLabel = 'منخفض';
                                    $stockClass = 'status-low';
                                } elseif ($balance['quantity'] >= $balance['max_stock_level'] && $balance['max_stock_level'] > 0) {
                                    $stockStatus = 'high';
                                    $stockLabel = 'مرتفع';
                                    $stockClass = 'status-high';
                                }
                                ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">
                                                    <?php echo htmlspecialchars($balance['item_name']); ?>
                                                </div>
                                                <div class="text-sm text-gray-500">
                                                    كود: <?php echo htmlspecialchars($balance['item_code']); ?>
                                                    <?php if (!empty($balance['category_name'])): ?>
                                                        | <?php echo htmlspecialchars($balance['category_name']); ?>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo htmlspecialchars($balance['warehouse_name']); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <div class="font-medium">
                                            <?php echo formatNumber($balance['available_quantity'], 3); ?>
                                            <?php echo htmlspecialchars($balance['unit_symbol']); ?>
                                        </div>
                                        <?php if ($balance['min_stock_level'] > 0): ?>
                                            <div class="text-xs text-gray-500">
                                                الحد الأدنى: <?php echo formatNumber($balance['min_stock_level'], 3); ?>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php if ($balance['reserved_quantity'] > 0): ?>
                                            <span class="text-orange-600 font-medium">
                                                <?php echo formatNumber($balance['reserved_quantity'], 3); ?>
                                                <?php echo htmlspecialchars($balance['unit_symbol']); ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-gray-400">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo formatMoney($balance['average_cost']); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                                        <?php echo formatMoney($balance['total_value']); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $stockClass; ?>">
                                            <?php echo $stockLabel; ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php if (!empty($balance['last_movement_date'])): ?>
                                            <?php echo formatDate($balance['last_movement_date']); ?>
                                        <?php else: ?>
                                            <span class="text-gray-400">-</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>

            <!-- ترقيم الصفحات -->
            <?php if ($totalPages > 1 && empty($searchTerm)): ?>
                <div class="px-6 py-4 border-t border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-700">
                            عرض <?php echo (($page - 1) * $itemsPerPage) + 1; ?> إلى
                            <?php echo min($page * $itemsPerPage, $totalItems); ?> من
                            <?php echo number_format($totalItems); ?> صنف
                        </div>
                        <div class="flex space-x-2 space-x-reverse">
                            <?php if ($page > 1): ?>
                                <a href="inventory.php?page=<?php echo $page - 1; ?><?php echo $warehouseFilter ? '&warehouse=' . $warehouseFilter : ''; ?><?php echo $lowStockOnly ? '&low_stock=1' : ''; ?>"
                                   class="px-3 py-2 text-sm bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition-colors duration-200">
                                    السابق
                                </a>
                            <?php endif; ?>

                            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                <a href="inventory.php?page=<?php echo $i; ?><?php echo $warehouseFilter ? '&warehouse=' . $warehouseFilter : ''; ?><?php echo $lowStockOnly ? '&low_stock=1' : ''; ?>"
                                   class="px-3 py-2 text-sm <?php echo $i == $page ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-700 hover:bg-gray-400'; ?> rounded transition-colors duration-200">
                                    <?php echo $i; ?>
                                </a>
                            <?php endfor; ?>

                            <?php if ($page < $totalPages): ?>
                                <a href="inventory.php?page=<?php echo $page + 1; ?><?php echo $warehouseFilter ? '&warehouse=' . $warehouseFilter : ''; ?><?php echo $lowStockOnly ? '&low_stock=1' : ''; ?>"
                                   class="px-3 py-2 text-sm bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition-colors duration-200">
                                    التالي
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

        </div>

    </div>

    <!-- تحسينات CSS للتفاعل -->
    <style>
        /* تحسين مظهر الجداول */
        table tr:hover {
            background-color: #f9fafb;
        }

        /* تحسين مظهر البطاقات */
        .card-hover:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        /* تحسين مظهر الأزرار */
        button:hover, a:hover {
            transform: translateY(-1px);
        }

        /* تحسين عرض الجداول على الشاشات الصغيرة */
        @media (max-width: 768px) {
            .table-responsive {
                font-size: 0.75rem;
            }

            .table-responsive th,
            .table-responsive td {
                padding: 0.5rem;
            }
        }
    </style>

</body>
</html>
