<?php
/**
 * صفحة ميزان المراجعة
 * Trial Balance Page
 */

define('APP_INIT', true);
require_once 'includes/init.php';

// التحقق من تسجيل الدخول والصلاحيات
requireLogin();
requirePermission('reports_view');

// إنشاء مثيلات النماذج
$transactionModel = new Transaction();
$accountModel = new Account();

// معالجة المرشحات
$dateFrom = sanitizeInput($_GET['date_from'] ?? '');
$dateTo = sanitizeInput($_GET['date_to'] ?? '');

// تعيين تواريخ افتراضية إذا لم تكن محددة
if (empty($dateFrom)) {
    $dateFrom = date('Y-01-01'); // بداية السنة الحالية
}
if (empty($dateTo)) {
    $dateTo = date('Y-m-d'); // اليوم الحالي
}

// الحصول على ميزان المراجعة
$trialBalance = $transactionModel->getTrialBalance($dateFrom, $dateTo);

// حساب الإجماليات
$totalDebit = 0;
$totalCredit = 0;
$totalBalance = 0;

foreach ($trialBalance as $account) {
    $totalDebit += $account['total_debit'];
    $totalCredit += $account['total_credit'];
    $totalBalance += $account['balance'];
}

// تجميع الحسابات حسب النوع
$accountsByType = [];
$accountTypes = $accountModel->getAccountTypes();

foreach ($trialBalance as $account) {
    $type = $account['account_type'];
    if (!isset($accountsByType[$type])) {
        $accountsByType[$type] = [
            'name' => $accountTypes[$type] ?? $type,
            'accounts' => [],
            'total_debit' => 0,
            'total_credit' => 0,
            'total_balance' => 0
        ];
    }
    
    $accountsByType[$type]['accounts'][] = $account;
    $accountsByType[$type]['total_debit'] += $account['total_debit'];
    $accountsByType[$type]['total_credit'] += $account['total_credit'];
    $accountsByType[$type]['total_balance'] += $account['balance'];
}

$pageTitle = 'ميزان المراجعة';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle . ' - ' . APP_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- خط عربي -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- أيقونات -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        
        /* تحسين عرض التقرير */
        .report-table {
            font-size: 0.875rem;
        }
        
        .account-type-header {
            background-color: #f3f4f6;
            font-weight: 600;
        }
        
        .account-type-assets { border-right: 4px solid #3b82f6; }
        .account-type-liabilities { border-right: 4px solid #ef4444; }
        .account-type-equity { border-right: 4px solid #10b981; }
        .account-type-revenue { border-right: 4px solid #f59e0b; }
        .account-type-expenses { border-right: 4px solid #8b5cf6; }
        
        .total-row {
            background-color: #1f2937;
            color: white;
            font-weight: 700;
        }
        
        @media print {
            .no-print { display: none; }
            body { font-size: 12px; }
            .report-table { font-size: 11px; }
        }
    </style>
</head>
<body class="bg-gray-50">

    <!-- شريط التنقل العلوي -->
    <nav class="gradient-bg shadow-lg no-print">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="dashboard.php" class="flex-shrink-0 flex items-center text-white hover:text-gray-200">
                        <i class="fas fa-arrow-right text-xl ml-3"></i>
                        <span>العودة للوحة التحكم</span>
                    </a>
                </div>
                
                <div class="flex items-center">
                    <h1 class="text-white text-xl font-bold">
                        <i class="fas fa-balance-scale ml-2"></i>
                        <?php echo $pageTitle; ?>
                    </h1>
                </div>
                
                <div class="flex items-center space-x-4 space-x-reverse">
                    <span class="text-white text-sm">
                        <i class="fas fa-user ml-1"></i>
                        <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                    </span>
                    <a href="logout.php" class="text-white hover:text-gray-200 transition-colors duration-200">
                        <i class="fas fa-sign-out-alt ml-1"></i>
                        خروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        
        <!-- عرض الرسائل -->
        <?php showAlert(); ?>
        
        <!-- ميزان المراجعة -->
        <div class="bg-white shadow-lg rounded-lg">
            
            <!-- رأس التقرير -->
            <div class="px-6 py-4 border-b border-gray-200 no-print">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-medium text-gray-900">
                        <i class="fas fa-balance-scale ml-2"></i>
                        ميزان المراجعة
                    </h3>
                    <div class="flex space-x-2 space-x-reverse">
                        <button onclick="window.print()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-print ml-2"></i>
                            طباعة
                        </button>
                        <a href="transactions.php" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-exchange-alt ml-2"></i>
                            القيود المحاسبية
                        </a>
                        <a href="accounts.php" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-chart-line ml-2"></i>
                            شجرة الحسابات
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- فلترة التواريخ -->
            <div class="px-6 py-4 bg-gray-50 border-b border-gray-200 no-print">
                <form method="GET" action="trial_balance.php" class="flex items-center space-x-4 space-x-reverse">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">من تاريخ</label>
                        <input 
                            type="date" 
                            name="date_from" 
                            value="<?php echo htmlspecialchars($dateFrom); ?>"
                            class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">إلى تاريخ</label>
                        <input 
                            type="date" 
                            name="date_to" 
                            value="<?php echo htmlspecialchars($dateTo); ?>"
                            class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                    </div>
                    <div class="pt-6">
                        <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-search ml-2"></i>
                            عرض
                        </button>
                    </div>
                </form>
            </div>
            
            <!-- معلومات التقرير -->
            <div class="px-6 py-4 bg-blue-50 border-b border-gray-200">
                <div class="text-center">
                    <h2 class="text-xl font-bold text-gray-900 mb-2"><?php echo APP_NAME; ?></h2>
                    <h3 class="text-lg font-semibold text-gray-800 mb-1">ميزان المراجعة</h3>
                    <p class="text-sm text-gray-600">
                        من <?php echo formatDate($dateFrom); ?> إلى <?php echo formatDate($dateTo); ?>
                    </p>
                    <p class="text-xs text-gray-500 mt-2">
                        تاريخ الطباعة: <?php echo formatDate(date('Y-m-d')); ?> - الوقت: <?php echo date('H:i'); ?>
                    </p>
                </div>
            </div>

            <!-- جدول ميزان المراجعة -->
            <div class="overflow-x-auto">
                <?php if (empty($trialBalance)): ?>
                    <div class="text-center py-12">
                        <i class="fas fa-balance-scale text-gray-400 text-6xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">لا توجد بيانات</h3>
                        <p class="text-gray-500">لا توجد معاملات في الفترة المحددة</p>
                    </div>
                <?php else: ?>
                    <table class="min-w-full divide-y divide-gray-200 report-table">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">كود الحساب</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">اسم الحساب</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">إجمالي المدين</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">إجمالي الدائن</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الرصيد</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">طبيعة الرصيد</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($accountsByType as $type => $typeData): ?>
                                <!-- رأس نوع الحساب -->
                                <tr class="account-type-header account-type-<?php echo $type; ?>">
                                    <td colspan="6" class="px-6 py-3 text-sm font-semibold text-gray-900">
                                        <?php echo htmlspecialchars($typeData['name']); ?>
                                    </td>
                                </tr>

                                <!-- حسابات النوع -->
                                <?php foreach ($typeData['accounts'] as $account): ?>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            <?php echo htmlspecialchars($account['account_code']); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php echo htmlspecialchars($account['account_name']); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-left">
                                            <?php echo $account['total_debit'] > 0 ? formatMoney($account['total_debit']) : '-'; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-left">
                                            <?php echo $account['total_credit'] > 0 ? formatMoney($account['total_credit']) : '-'; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-left">
                                            <?php if ($account['balance'] != 0): ?>
                                                <span class="<?php echo $account['balance'] > 0 ? 'text-green-600' : 'text-red-600'; ?>">
                                                    <?php echo formatMoney(abs($account['balance'])); ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="text-gray-400">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <?php if ($account['balance'] > 0): ?>
                                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">مدين</span>
                                            <?php elseif ($account['balance'] < 0): ?>
                                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">دائن</span>
                                            <?php else: ?>
                                                <span class="text-gray-400">متوازن</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>

                                <!-- إجمالي النوع -->
                                <tr class="bg-gray-100 border-t border-gray-300">
                                    <td colspan="2" class="px-6 py-3 text-sm font-semibold text-gray-900">
                                        إجمالي <?php echo htmlspecialchars($typeData['name']); ?>
                                    </td>
                                    <td class="px-6 py-3 text-sm font-bold text-gray-900 text-left">
                                        <?php echo formatMoney($typeData['total_debit']); ?>
                                    </td>
                                    <td class="px-6 py-3 text-sm font-bold text-gray-900 text-left">
                                        <?php echo formatMoney($typeData['total_credit']); ?>
                                    </td>
                                    <td class="px-6 py-3 text-sm font-bold text-left">
                                        <span class="<?php echo $typeData['total_balance'] > 0 ? 'text-green-600' : 'text-red-600'; ?>">
                                            <?php echo formatMoney(abs($typeData['total_balance'])); ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-3 text-sm font-semibold text-gray-700">
                                        <?php echo $typeData['total_balance'] > 0 ? 'مدين' : ($typeData['total_balance'] < 0 ? 'دائن' : 'متوازن'); ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>

                        <!-- الإجمالي العام -->
                        <tfoot>
                            <tr class="total-row">
                                <td colspan="2" class="px-6 py-4 text-sm font-bold">
                                    الإجمالي العام
                                </td>
                                <td class="px-6 py-4 text-sm font-bold text-left">
                                    <?php echo formatMoney($totalDebit); ?>
                                </td>
                                <td class="px-6 py-4 text-sm font-bold text-left">
                                    <?php echo formatMoney($totalCredit); ?>
                                </td>
                                <td class="px-6 py-4 text-sm font-bold text-left">
                                    <?php echo formatMoney(abs($totalBalance)); ?>
                                </td>
                                <td class="px-6 py-4 text-sm font-bold">
                                    <?php if (abs($totalDebit - $totalCredit) < 0.01): ?>
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">متوازن</span>
                                    <?php else: ?>
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">غير متوازن</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                <?php endif; ?>
            </div>

        </div>

    </div>

</body>
</html>
