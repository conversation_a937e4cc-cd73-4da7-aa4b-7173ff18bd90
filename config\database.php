<?php
/**
 * إعدادات قاعدة البيانات
 * Database Configuration
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'accounting_inventory_system');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// إعدادات الجلسة
define('SESSION_TIMEOUT', 3600); // ساعة واحدة
define('SESSION_NAME', 'accounting_session');

// إعدادات الأمان
define('CSRF_TOKEN_NAME', 'csrf_token');
define('PASSWORD_HASH_ALGO', PASSWORD_DEFAULT);

// إعدادات التطبيق
define('APP_NAME', 'نظام الحسابات والمخازن');
define('APP_VERSION', '1.0.0');
define('APP_URL', 'http://localhost/fizisoft');
define('TIMEZONE', 'Asia/Riyadh');

// إعدادات الملفات
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5 ميجابايت

// إعدادات التقارير
define('REPORTS_PATH', 'reports/');
define('PDF_FONT', 'DejaVu Sans');

// إعدادات التطوير
define('DEBUG_MODE', true);
define('ERROR_REPORTING', E_ALL);

// تعيين المنطقة الزمنية
date_default_timezone_set(TIMEZONE);

// تعيين الترميز
mb_internal_encoding('UTF-8');

// إعدادات PHP
if (DEBUG_MODE) {
    error_reporting(ERROR_REPORTING);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// إعدادات الجلسة
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 0); // تغيير إلى 1 في HTTPS
ini_set('session.gc_maxlifetime', SESSION_TIMEOUT);

?>
