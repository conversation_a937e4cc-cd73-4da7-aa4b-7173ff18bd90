/* 
 * ملف CSS للرسوم البيانية
 * Charts CSS File
 */

/* الأنماط العامة للرسوم البيانية */
.chart-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.chart-title {
    font-size: 16px;
    font-weight: 600;
    color: #374151;
    margin: 0 0 15px 0;
    text-align: center;
}

.chart-placeholder {
    text-align: center;
    color: #6b7280;
    padding: 40px 20px;
    font-style: italic;
}

/* الرسم البياني الدائري */
.pie-chart-container {
    text-align: center;
}

.pie-chart {
    position: relative;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    margin: 0 auto 20px;
    background: conic-gradient(
        #3b82f6 0deg 120deg,
        #ef4444 120deg 240deg,
        #10b981 240deg 360deg
    );
}

.chart-legend {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-width: 300px;
    margin: 0 auto;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 3px;
    flex-shrink: 0;
}

.legend-label {
    flex: 1;
    text-align: right;
}

.legend-value {
    color: #6b7280;
    font-weight: 500;
}

/* الرسم البياني العمودي */
.bar-chart-container {
    width: 100%;
}

.bar-chart {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.bar-item {
    display: flex;
    align-items: center;
    gap: 12px;
}

.bar-label {
    min-width: 100px;
    font-size: 14px;
    color: #374151;
    text-align: right;
}

.bar-container {
    flex: 1;
    position: relative;
    height: 30px;
    background: #f3f4f6;
    border-radius: 15px;
    overflow: hidden;
}

.bar-fill {
    height: 100%;
    border-radius: 15px;
    transition: width 0.3s ease;
    position: relative;
}

.bar-value {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 12px;
    font-weight: 600;
    color: #374151;
}

/* الرسم البياني الخطي */
.line-chart-container {
    width: 100%;
}

.line-chart {
    position: relative;
    height: 200px;
    background: linear-gradient(to bottom, transparent 0%, transparent 100%);
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
}

.line-svg {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
}

.line-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    font-size: 12px;
    color: #6b7280;
}

.line-label {
    text-align: center;
}

/* المؤشر الدائري */
.progress-circle-container {
    text-align: center;
}

.progress-circle {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto;
    border-radius: 50%;
    background: conic-gradient(
        var(--color, #3b82f6) 0deg calc(var(--progress, 0%) * 3.6deg),
        #e5e7eb calc(var(--progress, 0%) * 3.6deg) 360deg
    );
    display: flex;
    align-items: center;
    justify-content: center;
}

.progress-inner {
    width: 80px;
    height: 80px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.progress-text {
    font-size: 18px;
    font-weight: 600;
    color: #374151;
}

/* شريط التقدم */
.progress-bar-container {
    width: 100%;
}

.progress-bar {
    position: relative;
    height: 24px;
    background: #e5e7eb;
    border-radius: 12px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    border-radius: 12px;
    transition: width 0.3s ease;
}

.progress-label {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
    font-weight: 600;
    color: #374151;
}

/* المقياس */
.gauge-container {
    text-align: center;
}

.gauge {
    position: relative;
    width: 150px;
    height: 75px;
    margin: 0 auto;
    overflow: hidden;
}

.gauge-fill {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background: conic-gradient(
        var(--color, #3b82f6) 0deg var(--angle, 0deg),
        #e5e7eb var(--angle, 0deg) 180deg,
        transparent 180deg 360deg
    );
    transform: rotate(-90deg);
}

.gauge-center {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
}

.gauge-value {
    display: block;
    font-size: 18px;
    font-weight: 600;
    color: #374151;
}

.gauge-max {
    display: block;
    font-size: 12px;
    color: #6b7280;
}

/* جدول الإحصائيات */
.stats-table-container {
    width: 100%;
}

.stats-table {
    width: 100%;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #e5e7eb;
}

.stats-row {
    display: flex;
    border-bottom: 1px solid #e5e7eb;
}

.stats-row:last-child {
    border-bottom: none;
}

.stats-header {
    background: #f9fafb;
    font-weight: 600;
}

.stats-cell {
    flex: 1;
    padding: 12px;
    text-align: center;
    font-size: 14px;
}

.stats-header .stats-cell {
    color: #374151;
}

/* بطاقة الإحصائيات */
.stat-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #3b82f6;
    position: relative;
}

.stat-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.stat-icon {
    font-size: 20px;
}

.stat-title {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
    font-weight: 500;
}

.stat-value {
    font-size: 28px;
    font-weight: 700;
    color: #111827;
    margin-bottom: 8px;
}

.stat-subtitle {
    font-size: 12px;
    color: #6b7280;
    margin-bottom: 8px;
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 600;
}

.trend-up {
    color: #10b981;
}

.trend-down {
    color: #ef4444;
}

.trend-neutral {
    color: #6b7280;
}

/* الشبكة المتجاوبة */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

/* التجاوب مع الشاشات الصغيرة */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .charts-grid {
        grid-template-columns: 1fr;
    }
    
    .bar-item {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }
    
    .bar-label {
        min-width: auto;
        text-align: center;
    }
    
    .stats-row {
        flex-direction: column;
    }
    
    .stats-cell {
        text-align: right;
        border-bottom: 1px solid #f3f4f6;
    }
    
    .stats-cell:last-child {
        border-bottom: none;
    }
}

/* تأثيرات الحركة */
.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease;
}

.bar-fill:hover {
    opacity: 0.8;
}

/* ألوان إضافية للرسوم البيانية */
.color-blue { color: #3b82f6; }
.color-red { color: #ef4444; }
.color-green { color: #10b981; }
.color-yellow { color: #f59e0b; }
.color-purple { color: #8b5cf6; }
.color-cyan { color: #06b6d4; }
.color-orange { color: #f97316; }
.color-gray { color: #6b7280; }
