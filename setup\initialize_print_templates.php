<?php
/**
 * سكريبت تهيئة قوالب الطباعة الافتراضية
 * Initialize Default Print Templates Script
 */

// تعيين المسار الأساسي
define('BASE_PATH', dirname(__DIR__) . '/');

// تضمين ملفات النظام
require_once BASE_PATH . 'includes/config.php';
require_once BASE_PATH . 'includes/database.php';
require_once BASE_PATH . 'includes/functions.php';
require_once BASE_PATH . 'models/PrintTemplate.php';

// التحقق من أن الملف يتم تشغيله من سطر الأوامر أو من المتصفح مع معاملات صحيحة
$isCommandLine = php_sapi_name() === 'cli';
$hasValidToken = isset($_GET['token']) && $_GET['token'] === 'init_templates_2024';

if (!$isCommandLine && !$hasValidToken) {
    die('غير مسموح بالوصول المباشر');
}

echo $isCommandLine ? "بدء تهيئة قوالب الطباعة الافتراضية...\n" : "<h2>بدء تهيئة قوالب الطباعة الافتراضية...</h2>";

try {
    // إنشاء مثيل نموذج قوالب الطباعة
    $templateModel = new PrintTemplate();
    
    // تهيئة القوالب الافتراضية
    $result = $templateModel->createDefaultTemplates();
    
    if ($result) {
        echo $isCommandLine ? "تم تهيئة قوالب الطباعة الافتراضية بنجاح!\n" : "<p style='color: green;'>تم تهيئة قوالب الطباعة الافتراضية بنجاح!</p>";
        
        // عرض القوالب المهيأة
        $allTemplates = $templateModel->getAllTemplates();
        
        echo $isCommandLine ? "\nالقوالب المهيأة:\n" : "<h3>القوالب المهيأة:</h3><ul>";
        
        foreach ($allTemplates as $template) {
            $templateInfo = "{$template['template_name']} ({$template['template_type']})";
            if ($template['is_default']) {
                $templateInfo .= " - افتراضي";
            }
            
            if ($isCommandLine) {
                echo "- {$templateInfo}\n";
            } else {
                echo "<li>{$templateInfo}</li>";
            }
        }
        
        if (!$isCommandLine) {
            echo "</ul>";
        }
        
        echo $isCommandLine ? "\nتم الانتهاء من التهيئة.\n" : "<p>تم الانتهاء من التهيئة.</p>";
        
    } else {
        echo $isCommandLine ? "فشل في تهيئة قوالب الطباعة!\n" : "<p style='color: red;'>فشل في تهيئة قوالب الطباعة!</p>";
    }
    
} catch (Exception $e) {
    $errorMessage = "خطأ في تهيئة قوالب الطباعة: " . $e->getMessage();
    echo $isCommandLine ? $errorMessage . "\n" : "<p style='color: red;'>{$errorMessage}</p>";
}

if (!$isCommandLine) {
    echo "<br><a href='../print_templates.php'>الذهاب لإدارة قوالب الطباعة</a>";
    echo "<br><a href='../dashboard.php'>العودة للوحة التحكم</a>";
}

?>
